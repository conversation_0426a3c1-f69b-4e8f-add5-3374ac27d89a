<!DOCTYPE html><!--  Last Published: Sun Dec 22 2024 21:22:50 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="663a1aaff0e4426bba7f7258" data-wf-site="663245c7d597883474e88492">
<head>
  <meta charset="utf-8">
  <title>Flat18 — Pricing</title>
  <meta content="Explore Flat 18’s transparent pricing options for custom design and development services. Choose from flexible subscription plans that offer ongoing support for website, landing page, and Webflow site needs, ideal for startups, small businesses, and entrepreneurs" name="description">
  <meta content="Flat18 — Pricing" property="og:title">
  <meta content="Explore Flat 18’s transparent pricing options for custom design and development services. Choose from flexible subscription plans that offer ongoing support for website, landing page, and Webflow site needs, ideal for startups, small businesses, and entrepreneurs" property="og:description">
  <meta content="https://flat18.co.uk/static/advert-flat-18-f18-og_1-p-2000.webp" property="og:image">
  <meta content="Flat18 — Pricing" property="twitter:title">
  <meta content="Explore Flat 18’s transparent pricing options for custom design and development services. Choose from flexible subscription plans that offer ongoing support for website, landing page, and Webflow site needs, ideal for startups, small businesses, and entrepreneurs" property="twitter:description">
  <meta content="https://flat18.co.uk/static/advert-flat-18-f18-og_1-p-2000.webp" property="twitter:image">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/flat18.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <script type="text/javascript">!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.agent='plwebflow';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,document,'script','https://connect.facebook.net/en_US/fbevents.js');fbq('init', '745776286716427');fbq('track', 'PageView');</script>
  <style>
  [class*="btn"]:hover > [class*="button-background"]{
  	transform: translateX(50%);
  }
    [class*="btn"]:hover > [class*="icon"][class*="right"]{
  	transform: translateX(5px);
  }
  .animated-counter::after {
    content: '+';
}
</style>
  <script>
    if(window.location.protocol != 'https:') {
      if(location.href.indexOf("808")<0)
      location.href = location.href.replace("http://", "https://")
    }
    const q = localStorage && localStorage.getItem("webM")? `&webM=${localStorage.getItem("webM")}` : ""
    fetch('https://api.flat18.co.uk/metrics/webm/index.php?geo=1' + q)
      .then(response => response.json())
      .then(data => {
        window.webM = data.webM
        window.geoCityCountry = data.geo
        let persist = localStorage && localStorage.getItem("webM")? localStorage.getItem("webM") : data.webM
        localStorage.setItem("webM", persist)
      });
  </script>
  <script defer="" src="https://eu.umami.is/script.js" data-website-id="54c1aa36-ac18-426d-ba14-3d5827cfa465"></script>
  <script async="" src="https://master--melodic-taffy-1a4c18.netlify.app/tracker.js" data-ackee-server="https://master--melodic-taffy-1a4c18.netlify.app" data-ackee-domain-id="b28e2698-bf04-4e23-9075-a5f7110affe0"></script>
  <!--  Twitter conversion tracking base code  -->
  <script>
!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
twq('config','oopi3');
</script>
  <!--  End Twitter conversion tracking base code  -->
</head>
<body class="body">
  <div class="body-wrapper">
    <div class="navbar-no-shadow">
      <div data-animation="default" data-collapse="medium" data-duration="400" data-easing="ease" data-easing2="ease" role="banner" class="navbar-no-shadow-container w-nav">
        <div class="container-regular">
          <div class="navbar-wrapper">
            <a href="index.html" class="navbar-brand w-nav-brand">
              <div class="homepage-loader"><img src="images/flat18_256x256.avif" loading="lazy" alt="" class="image-11"></div>
              <div class="words-laft-eitheeen">Flat 18</div>
            </a>
            <nav role="navigation" class="nav-menu-wrapper w-nav-menu">
              <div class="div-block">
                <ul role="list" class="nav-menu w-list-unstyled">
                  <li>
                    <a link="#chat" href="#" class="nav-link">Chat</a>
                  </li>
                  <li>
                    <a href="/#pricing" class="nav-link">Pricing</a>
                  </li>
                  <li>
                    <a href="https://accounts.flat18.co.uk/client/login" class="nav-link">Login</a>
                  </li>
                  <li>
                    <a href="/#wordsExperimentsSliderContainerWrapper" class="nav-link">Work</a>
                  </li>
                  <li class="mobile-margin-top-10">
                    <div class="nav-button-wrapper">
                      <div link="#chat" class="btn menu">
                        <div class="button-background menu"></div>
                        <div class="button-text menu">Get started</div>
                        <div class="icon right w-embed">&#xF135;</div>
                      </div>
                    </div>
                  </li>
                </ul>
                <div class="w-embed">
                  <style>
[class*="nav-overlay"]{
    background-color: var(--bg-gauze-heavy);
    background-image: linear-gradient(90deg, var(--bg-gauze) 34%, var(--bg-modern-dark));
    -webkit-backdrop-filter: blur(80px);
    backdrop-filter: blur(80px);
}
</style>
                </div>
              </div>
            </nav>
            <div class="menu-button w-nav-button">
              <div class="icon right icon-menu w-embed">&#xF479;</div>
              <div class="icon right icon-close w-embed">&#xF62A;</div>
              <div class="icon right w-embed">
                <style>
.menu-button[class*="open"] .icon-menu{display:none;}
.menu-button:not([class*="open"]) .icon-close{display:none;}
</style>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="more-info" class="content vertical">
      <h1 class="gradient-text doc-heading">Full Service Development <br>—How we Cost our Services</h1>
      <div class="text-block-reading">We are full-stack developers with a flair for design. <br>That means, you&#x27;ll get a comprehensive service with the full scope managed under one roof.<br><br>By applying our hourly rate, £37.50 to an estimated 80 development hours /pcm, we arrive at a monthly cost of ~£3,000 /month.<br><br>We operate very limited active spaces per month. Usually no more than 2 active clients per developer. This ensures you and your project get the attention required. Booking our services for longer periods, rather than month-to-month, helps ease our scheduling projections and we think you should be rewarded for that. Quarterly billing offers you the best price (around 15% discount).<br><br>Come have a chat with us about your project.<br>It&#x27;s free and there&#x27;s no obligation 🙂</div>
      <aside link="#chat" class="btn">
        <div class="button-background"></div>
        <div class="button-text">Chat with us</div>
        <div class="icon small w-embed">&#xF24C;</div>
      </aside>
    </div>
    <section id="pricing" data-w-id="ccc7714e-4111-33db-545c-124e3954058f" class="content vert content-build-in-target">
      <div class="w-layout-vflex text-org subscriptions">
        <h2 class="gradient-text yellow">Pricing. Simple.</h2>
      </div>
      <div class="pricing-details-wrapper">
        <div class="pricing-head dramatic">
          <div id="w-node-d23227f8-aab5-0c14-c064-709c4c129f2f-3954058f" class="content-imagery-wrapper">
            <div class="w-layout-vflex pricing-controls-container small-screens">
              <div data-animation="over-right" data-collapse="all" data-duration="400" data-easing="ease" data-easing2="ease" role="banner" class="currency-dropdown-menu w-nav">
                <div class="menu-container w-container">
                  <div class="trigger-currency-dropdown-menu w-nav-button">
                    <div class="text-block-5">Change currency</div>
                  </div>
                  <nav role="navigation" class="currency-menu-wrapper w-nav-menu">
                    <div class="w-layout-vflex currency-menu">
                      <a href="#" class="currency-link w-nav-link">GBP</a>
                      <a href="#" class="currency-link w-nav-link">GBP</a>
                      <a href="#" class="currency-link w-nav-link">GBP</a>
                      <a href="#" class="currency-link w-nav-link">GBP</a>
                      <a href="#" class="currency-link w-nav-link">GBP</a>
                    </div>
                  </nav>
                </div>
              </div>
              <div class="price-wrapper">
                <div class="pricing-price monthly">
                  <div class="w-layout-hflex price-display">
                    <div class="price-exchange">£2,995</div>
                    <div class="month">/month</div>
                  </div>
                </div>
                <div class="pricing-price quarterly">
                  <div class="w-layout-hflex price-display">
                    <div class="price-exchange">£2,495</div>
                    <div class="month">/month</div>
                  </div>
                </div>
              </div>
              <div class="switch-wrapper">
                <div fn="billing-type" class="switch-body billing-type on">
                  <div class="switch-indicator"></div>
                </div>
                <div class="w-layout-hflex switch-label-wrapper quarterly">
                  <div class="switch-label">You&#x27;re saving <span class="price-exchange">£1,500</span> with quarterly billing</div>
                </div>
                <div class="w-layout-hflex switch-label-wrapper monthly">
                  <div class="switch-label">Billed Monthly<br>Activate savings with quarterly billing</div>
                </div>
              </div>
            </div>
          </div>
          <div class="w-layout-vflex flex-block-3">
            <div class="w-layout-vflex bricing-details-wrapper">
              <div class="badge subtle left solid">What&#x27;s included</div>
              <ul role="list" class="pricing-feature-list wide columns w-list-unstyled">
                <li class="pricing-list-item things-are-different">
                  <div class="icon small w-embed">&#xF26E;</div>
                  <div class="pricing-feature">Queued Tasks delivered in as little as 48hrs</div>
                </li>
                <li class="pricing-list-item things-are-different">
                  <div class="icon small w-embed">&#xF26E;</div>
                  <div class="pricing-feature">Unlimited Development Scopes</div>
                </li>
                <li class="pricing-list-item things-are-different">
                  <div class="icon small w-embed">&#xF26E;</div>
                  <div class="pricing-feature">Application staging</div>
                </li>
                <li class="pricing-list-item things-are-different">
                  <div class="icon small w-embed">&#xF26E;</div>
                  <div class="pricing-feature">Unlimited Revisions queue</div>
                </li>
                <li class="pricing-list-item things-are-different">
                  <div class="icon small w-embed">&#xF26E;</div>
                  <div class="pricing-feature">AI &amp; Custom Graphics</div>
                </li>
                <li class="pricing-list-item things-are-different">
                  <div class="icon small w-embed">&#xF26E;</div>
                  <div class="pricing-feature">Complete Service Management</div>
                </li>
                <li class="pricing-list-item things-are-different">
                  <div class="icon small w-embed">&#xF26E;</div>
                  <div class="pricing-feature">Support directly from your developer</div>
                </li>
                <li class="pricing-list-item things-are-different">
                  <div class="icon small w-embed">&#xF26E;</div>
                  <div class="pricing-feature">Pause and resume week-by-week. Bank all your unused time</div>
                </li>
              </ul>
            </div>
            <div class="w-layout-vflex bricing-details-wrapper dark">
              <div class="badge subtle left green bold">Billing</div>
              <ul role="list" class="pricing-feature-list wide w-list-unstyled">
                <li class="pricing-list-item monthly">
                  <div class="icon small w-embed">&#xF1E2;</div>
                  <div class="pricing-feature">Monthly billing</div>
                </li>
                <li class="pricing-list-item quarterly">
                  <div class="icon small w-embed">&#xF64A;</div>
                  <div class="pricing-feature">Quarterly billing</div>
                </li>
                <li class="pricing-list-item quarterly">
                  <div class="icon small w-embed">&#xF247;</div>
                  <div class="w-layout-hflex inline-text-blocks">
                    <div class="pricing-feature">Save</div>
                    <div class="pricing-feature price-exchange">£1,500</div>
                    <div class="pricing-feature">v monthly</div>
                  </div>
                </li>
                <li class="pricing-list-item monthly">
                  <div class="icon small w-embed">&#xF117;</div>
                  <div class="w-layout-hflex inline-text-blocks">
                    <div class="pricing-feature">Pre-pay</div>
                    <div class="pricing-feature price-exchange">£2,995</div>
                    <div class="pricing-feature">every month</div>
                  </div>
                </li>
                <li class="pricing-list-item quarterly">
                  <div class="icon small w-embed">&#xF117;</div>
                  <div class="w-layout-hflex inline-text-blocks">
                    <div class="pricing-feature">Pre-pay</div>
                    <div class="pricing-feature price-exchange">£7,485</div>
                    <div class="pricing-feature">every 3 months</div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="w-layout-hflex btn-flex orientate-right">
          <a href="/pricing#more-info" class="btn link w-inline-block">
            <div class="button-text">Learn more</div>
            <div class="icon btn-icon w-embed">&#xF135;</div>
          </a>
          <a link="#chat" href="#" class="btn w-inline-block">
            <div class="button-background"></div>
            <div class="button-text">Let&#x27;s talk about pricing</div>
            <div class="icon btn-icon w-embed">&#xF715;</div>
          </a>
        </div>
        <div class="style code-embed w-embed">
          <style>
	.switch-body:not(on):hover .switch-indicator{
  	transform: translateX(4px);
  }
  .switch-body.on:hover .switch-indicator{
    transform: translateX(calc(100% - 2px));
  }
  .switch-body.on{
  	box-shadow: 0 0 0 2px var(--primary);
  }
  .switch-body.on .switch-indicator{
  	background: var(--primary);
    transform: translateX(100%);
  }
  .rotunda{
  animation: rotateRot 120s linear infinite;
  }
    .rotunda.two{
  animation: rotateRot 90s linear reverse infinite;
  }
      .rotunda.three{
  animation: rotateRot 30s linear infinite;
  }
  @keyframes rotateRot {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(-360deg);
    }
}
.pulsate{
animation: pulsateShadow 5s ease-in-out infinite;}
@keyframes pulsateShadow {
    0%, 100% {
        box-shadow:0 0 0 0px var(--azure-blue),
        0 0 0 1px var(--azure-blue),  
        0 10px 50px 0 var(--bg); /* Initial and final shadow */
    }
    50% {
        box-shadow:0 0 0px 15px #10a3db7d, 
        0 0 0 1px var(--azure-blue), 
        0 10px 50px 0 var(--bg); /* Stronger shadow at the middle */
    }
}
[class*="pricing"] .w-nav-overlay, [class*="pricing"] nav {
    height: auto !important;
    background: unset !important;
    -webkit-backdrop-filter: unset !important;
    backdrop-filter:  unset !important;
}
[class*="pricing"] .w-nav-overlay{
    top: 2em !important;
}
</style>
        </div>
        <div class="script code-embed w-embed w-script">
          <script>
  for (const ele of document.querySelectorAll('.switch-body')) {
    ele.addEventListener("click", (e) => {
      if (e.target.classList.contains('on')) {
        e.target.classList.remove('on')
      } else {
        e.target.classList.add('on')
      }
    })
  }
  for (const billingSwitch of document.querySelectorAll('.billing-type')) {
    billingSwitch.addEventListener("click", (e) => {
      if (e.target.classList.contains('on')) {
        document.querySelectorAll(".monthly").forEach((ele) => { ele.style.display = "none" });
        document.querySelectorAll(".quarterly").forEach((ele) => { ele.style.display = "" });
      } else {
        document.querySelectorAll(".monthly").forEach((ele) => { ele.style.display = "" });
        document.querySelectorAll(".quarterly").forEach((ele) => { ele.style.display = "none" });
      }
    })
  }
  document.querySelectorAll(".monthly").forEach((ele) => { ele.style.display = "none" });
  document.querySelectorAll(".quarterly").forEach((ele) => { ele.style.display = "" });
</script>
          <script>
  function closeCurrencyMenu() {
    const currencyMenu = document.querySelector(".currency-menu");
    if (currencyMenu) {
      const navOverlay = currencyMenu.closest('[class*="nav-overlay"]');
      if (navOverlay) {
        // Hide the nav-overlay
        navOverlay.style.display = "none";
        // Find a sibling with [class*="nav-button"] and update attributes/classes
        const navButton = navOverlay.parentElement.querySelector('[class*="nav-button"]');
        if (navButton) {
          navButton.setAttribute("aria-expanded", "false");
          navButton.classList.remove("w--open");
        }
      }
    }
  }
  async function getForexData() {
    try {
      const response = await fetch('https://f18-pay-backend.vercel.app/api/v1/forex', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      if (data.result) {
        const gbp = data.result.find(item => item.name === "GBP");
        document.querySelectorAll(".price-exchange").forEach((ele) => {
          const raw = ele.innerHTML.replace(/[^0-9]/g, '');
          const btcValue = Number(raw) / Number(gbp.value);
          for (const currency of data.result) {
            const exchangedValue = Number(currency.value) * btcValue;
            let formattedValue;
            // Handle known currency codes
            try {
              let fraction = exchangedValue > 1000 ? 0 : 5;
              let workingValue = exchangedValue > 1000
                ? (Math.ceil(exchangedValue / 10) * 10) - 5
                : exchangedValue;
              const formatted = new Intl.NumberFormat(navigator.language, {
                style: 'currency',
                minimumFractionDigits: fraction,
                maximumFractionDigits: fraction,
                currency: currency.name
              }).formatToParts(workingValue);
              // Wrap the currency in a <span>
              formattedValue = formatted.map(part => {
                if (part.type === 'currency') {
                  return `<span>${part.value}</span>`;
                }
                return part.value;
              }).join('');
            } catch (error) {
              // Fallback for unsupported currencies like cryptocurrencies
              if (error instanceof RangeError) {
                formattedValue = exchangedValue.toLocaleString(navigator.language, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ` <span>${currency.name}</span>`;
              } else {
                throw error;
              }
            }
            ele.setAttribute(`data-value-${currency.name.toLowerCase()}`, formattedValue);
          }
          ele.setAttribute(`data-value-btc`, `${btcValue.toFixed(6)} BTC`);
        });
        // Populate the currency menu
        updateCurrencyMenu(data.result);
        // Automatically set the appropriate currency based on browser language
        const defaultCurrency = detectCurrencyFromLanguage(navigator.language, data.result);
        applyCurrency(defaultCurrency, data.result);
      }
    } catch (error) {
      console.error('Error fetching forex data:', error);
    }
  }
  // Detect appropriate currency based on browser language
  function detectCurrencyFromLanguage(language, availableCurrencies) {
    const currencyMapping = {
      'en-GB': 'GBP',
      'en-US': 'USD',
      'fr-FR': 'EUR',
      'de-DE': 'EUR',
      'es-ES': 'EUR',
      'ja-JP': 'JPY',
      'zh-CN': 'CNY'
      // Add more mappings as needed
    };
    // Get the mapped currency or default to USD
    const currencyCode = currencyMapping[language] || currencyMapping[language.split('-')[0]] || 'USD';
    // Ensure the detected currency is available, fallback to USD if not
    return availableCurrencies.some(currency => currency.name === currencyCode)
      ? currencyCode
      : 'USD';
  }
  // Apply the detected or selected currency to all price elements
  function applyCurrency(currencyCode, availableCurrencies) {
    document.querySelectorAll(".price-exchange").forEach((pricingEle) => {
      if (pricingEle.hasAttribute(`data-value-${currencyCode.toLowerCase()}`)) {
        pricingEle.innerHTML = pricingEle.getAttribute(`data-value-${currencyCode.toLowerCase()}`);
      }
    });
    // Highlight the selected currency in the menu
    document.querySelectorAll(".currency-link").forEach(link => {
      link.classList.toggle('active', link.innerHTML === currencyCode);
    });
    closeCurrencyMenu();
  }
  // Update the currency menu with available options
  function updateCurrencyMenu(availableCurrencies) {
    const currencyMenu = document.querySelector(".currency-menu");
    currencyMenu.innerHTML = ""; // Clear existing menu items
    availableCurrencies.push({ name: 'BTC' }); // Add BTC as an option
    for (const currency of availableCurrencies) {
      const currencyShortcut = document.createElement("a");
      currencyShortcut.innerHTML = currency.name;
      currencyShortcut.classList.add("currency-link");
      currencyShortcut.addEventListener("click", () => {
        applyCurrency(currency.name, availableCurrencies);
      });
      currencyMenu.appendChild(currencyShortcut);
    }
  }
  // Fetch forex data and initialise the UI
  getForexData();
</script>
        </div>
      </div>
    </section>
    <section class="footer-dark">
      <div class="content">
        <div class="footer-wrapper">
          <a href="#" class="footer-brand w-inline-block"><img src="images/logo-24-blue.svg" loading="lazy" alt="" height="30" class="image-4"></a>
          <div class="footer-content">
            <div id="w-node-e28b2c83-3d20-f0ea-2b37-5ec141346fa7-41346f98" class="footer-block">
              <div class="title-small">Contact</div>
              <a link="#chat" href="#" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF24B;</div>
                <div>Live Chat</div>
              </a>
              <a href="https://t.me/flat18_bot" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF5B3;</div>
                <div>Telegram</div>
              </a>
              <a href="mailto:<EMAIL>" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF73B;</div>
                <div><EMAIL></div>
              </a>
              <a href="https://x.com/f18_dev" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF5EF;</div>
                <div>Twitter</div>
              </a>
            </div>
            <div id="w-node-e28b2c83-3d20-f0ea-2b37-5ec141346f9e-41346f98" class="footer-block">
              <div class="title-small">Resources</div>
              <a href="https://stats.uptimerobot.com/RKBX3irMJl" class="footer-link w-inline-block">
                <div class="status good"></div>
                <div>Service status<br></div>
              </a>
              <a href="about.html" class="footer-link">About us</a>
              <a href="ease-of-communication-standard.html" class="footer-link">Standards</a>
              <a href="privacy.html" class="footer-link">Privacy Policy</a>
              <a href="donate-to-flat18.html" class="footer-link">Donations</a>
              <a href="free-services.html" class="footer-link">Free Services</a>
            </div>
            <div id="w-node-_26beecda-c968-eb58-1bc5-790c38ae2b87-41346f98" class="footer-block">
              <div class="title-small">Shortcuts</div>
              <a href="pricing.html" aria-current="page" class="footer-link w--current">Pricing</a>
              <a href="terms.html" class="footer-link">Terms of Service</a>
              <a href="https://accounts.flat18.co.uk/client/login" class="footer-link">Login</a>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-copyright-center">© 2012-<span current-year="true">20</span> FLAT 18</div>
      <div class="code w-embed w-script">
        <script>
for(const year of document.querySelectorAll(`[current-year="true"]`)){
year.innerHTML = new Date().getFullYear()
}
</script>
        <style>
.status.good{
 animation: pulseRing 3s linear forwards infinite;
}
@keyframes pulseRing{
0%{
	box-shadow:0px 0px 0px 0px #00785c;
}
100%{
	box-shadow:0px 0px 0px 5px #00785c00;
}
}
</style>
      </div>
      <div class="w-layout-hflex cookie-notice">
        <div id="w-node-_6c147dfb-07db-e23e-9b2b-165178d9814e-41346f98" class="w-layout-vflex flex-block-2">
          <div class="accept-cookies large-only">This website uses cookies to enhance your browsing experience. Click here to dismiss this notice and accept cookies.<br></div>
          <div class="accept-cookies small-only">We use cookies to enhance your experience.<br>Tap here to accept and close.</div>
          <a href="privacy.html" class="link cookie">Learn more in our Privacy Policy</a>
        </div>
        <div id="w-node-_6ec3e2e3-cf69-9240-e231-fc58c0b85d27-41346f98" class="icon cookie accept-cookies w-embed">&#xF659;</div>
        <div class="code w-embed w-script">
          <script>
  const userAcknowledgedCookie = localStorage.getItem("userAcceptedCookies") ? localStorage.getItem("userAcceptedCookies") : 'false'
  localStorage.setItem("userAcceptedCookies", userAcknowledgedCookie)
  const bodyClassUserCookies = userAcknowledgedCookie !== 'false' ? 'cookies-accepted':'cookies-not-accepted'
  document.body.classList.add(bodyClassUserCookies)
  document.querySelectorAll(".accept-cookies").forEach((e)=>{
    e.addEventListener("click", ()=>{
      localStorage.setItem("userAcceptedCookies", 'true')
      document.body.classList.add('cookies-accepted')
    })
  })
</script>
          <style>
.cookies-not-accepted .cookie-notice{display: flex;}
.cookies-accepted .cookie-notice{display: none;}
</style>
        </div>
      </div>
    </section>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=663245c7d597883474e88492" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script>
  let theme = localStorage && localStorage.getItem('theme') ? (localStorage.getItem('theme') === 'dark' ? 'auto' : 'light') : 'auto'
  let size = window.outerWidth >= 756 ? "expanded_bubble" : "standard"
  window.chatwootSettings = {
    position: "right", type: size, launcherTitle: "Start here",
    darkMode: theme,
  };
  function initCW(d, t) {
    var BASE_URL = "https://chatwoot.flat18.co.uk";//;"https://app.chatwoot.com"
    var g = d.createElement(t), s = d.getElementsByTagName(t)[0];
    g.src = BASE_URL + "/packs/js/sdk.js";
    g.defer = true;
    g.async = true;
    g.classList.add("chatwoot-script-element")
    s.parentNode.insertBefore(g, s);
    g.onload = function () {
      window.chatwootSDK.run({
        websiteToken: 'krt1otbtLdpkie19rPwPThai',//'jvPpSh5d5zxrQDnanqRYtRx9',
        baseUrl: BASE_URL
      })
    }
  }
  initCW(document, "script")
  function makeid(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() *
        charactersLength));
    }
    return result;
  }
  window.addEventListener("chatwoot:ready", function () {
    // Use window.$chatwoot here
    // ...
    // let random = Date.now() + makeid(8)
    // window.$chatwoot.setUser(random, {
    //   //email: "<<EMAIL>>",
    //   //name: "<name-of-the-user>",
    //   avatar_url: "/src/img/avi.svg",
    //   //phone_number: "<phone-number-of-the-user>",
    // });
    let webMLocal = localStorage && localStorage.getItem("webM") ? localStorage.getItem("webM") : data.webM
    window.$chatwoot.setUser(webMLocal, { name: `${window.geoCityCountry} - ${webMLocal}` });
  });
  function observeParentOpacityChange(targetElement, callback) {
    // Get the grandparent element (parent > parent > parent)
    const grandparentElement = targetElement.parentElement?.parentElement?.parentElement;
    if (!grandparentElement) {
      console.warn('Grandparent element not found.');
      return;
    }
    // Create a callback function to execute when the opacity of the grandparent changes to 1
    function onOpacityChange(mutationsList, observer) {
      for (let mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const currentOpacity = window.getComputedStyle(grandparentElement).opacity;
          if (currentOpacity === '1') {
            // Trigger the provided callback function
            callback();
            // Disconnect the observer
            observer.disconnect();
            console.log('Observer disconnected.');
          }
        }
      }
    }
    // Create a MutationObserver instance and pass in the callback function
    const observer = new MutationObserver(onOpacityChange);
    // Start observing the grandparent element for attribute changes
    observer.observe(grandparentElement, { attributes: true });
  }
  // Original loop, modified to use the observer
  for (const ele of document.querySelectorAll(`[class*="animated-counter"]`)) {
    // Wrap your original setTimeout logic in a function
    const startAnimation = () => {
      let eleCount = 0;
      setTimeout(() => {
        let numb = Number(ele.innerHTML);
        let newNumb = 0;
        setInterval(() => {
          if (newNumb <= numb) {
            ele.innerHTML = newNumb;
            newNumb++;
          }
        }, 1500 / numb);
      }, 200 * eleCount);
      eleCount++;
    };
    // Use the observer to trigger the animation when the grandparent's opacity becomes 1
    observeParentOpacityChange(ele, startAnimation);
  }
  for (const chat of document.querySelectorAll(`[link*="chat"]`)) {
    chat.addEventListener("click", () => {
      window.$chatwoot.toggle("open")
      //Twitter Conversion Event
      twq('event', 'tw-oopi3-ooy6e', {
      });
    })
  }
</script>
</body>
</html>