<!DOCTYPE html><!--  Last Published: Sun Dec 22 2024 21:22:50 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="663983b46b8223290dfd0d1f" data-wf-site="663245c7d597883474e88492">
<head>
  <meta charset="utf-8">
  <title>Flat18 — Privacy policy</title>
  <meta content="Flat 18’s Privacy Policy outlines how we collect, use, and protect your data while providing high-quality design and development services. Learn about our commitment to transparency and security, ensuring your information is safeguarded across all interactions." name="description">
  <meta content="Flat18 — Privacy policy" property="og:title">
  <meta content="Flat 18’s Privacy Policy outlines how we collect, use, and protect your data while providing high-quality design and development services. Learn about our commitment to transparency and security, ensuring your information is safeguarded across all interactions." property="og:description">
  <meta content="https://flat18.co.uk/static/advert-flat-18-f18-og_1-p-2000.webp" property="og:image">
  <meta content="Flat18 — Privacy policy" property="twitter:title">
  <meta content="Flat 18’s Privacy Policy outlines how we collect, use, and protect your data while providing high-quality design and development services. Learn about our commitment to transparency and security, ensuring your information is safeguarded across all interactions." property="twitter:description">
  <meta content="https://flat18.co.uk/static/advert-flat-18-f18-og_1-p-2000.webp" property="twitter:image">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/flat18.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <script type="text/javascript">!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.agent='plwebflow';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,document,'script','https://connect.facebook.net/en_US/fbevents.js');fbq('init', '745776286716427');fbq('track', 'PageView');</script>
  <style>
  [class*="btn"]:hover > [class*="button-background"]{
  	transform: translateX(50%);
  }
    [class*="btn"]:hover > [class*="icon"][class*="right"]{
  	transform: translateX(5px);
  }
  .animated-counter::after {
    content: '+';
}
</style>
  <script>
    if(window.location.protocol != 'https:') {
      if(location.href.indexOf("808")<0)
      location.href = location.href.replace("http://", "https://")
    }
    const q = localStorage && localStorage.getItem("webM")? `&webM=${localStorage.getItem("webM")}` : ""
    fetch('https://api.flat18.co.uk/metrics/webm/index.php?geo=1' + q)
      .then(response => response.json())
      .then(data => {
        window.webM = data.webM
        window.geoCityCountry = data.geo
        let persist = localStorage && localStorage.getItem("webM")? localStorage.getItem("webM") : data.webM
        localStorage.setItem("webM", persist)
      });
  </script>
  <script defer="" src="https://eu.umami.is/script.js" data-website-id="54c1aa36-ac18-426d-ba14-3d5827cfa465"></script>
  <script async="" src="https://master--melodic-taffy-1a4c18.netlify.app/tracker.js" data-ackee-server="https://master--melodic-taffy-1a4c18.netlify.app" data-ackee-domain-id="b28e2698-bf04-4e23-9075-a5f7110affe0"></script>
  <!--  Twitter conversion tracking base code  -->
  <script>
!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
twq('config','oopi3');
</script>
  <!--  End Twitter conversion tracking base code  -->
</head>
<body class="body">
  <div class="body-wrapper">
    <div class="navbar-no-shadow">
      <div data-animation="default" data-collapse="medium" data-duration="400" data-easing="ease" data-easing2="ease" role="banner" class="navbar-no-shadow-container w-nav">
        <div class="container-regular">
          <div class="navbar-wrapper">
            <a href="index.html" class="navbar-brand w-nav-brand">
              <div class="homepage-loader"><img src="images/flat18_256x256.avif" loading="lazy" alt="" class="image-11"></div>
              <div class="words-laft-eitheeen">Flat 18</div>
            </a>
            <nav role="navigation" class="nav-menu-wrapper w-nav-menu">
              <div class="div-block">
                <ul role="list" class="nav-menu w-list-unstyled">
                  <li>
                    <a link="#chat" href="#" class="nav-link">Chat</a>
                  </li>
                  <li>
                    <a href="/#pricing" class="nav-link">Pricing</a>
                  </li>
                  <li>
                    <a href="https://accounts.flat18.co.uk/client/login" class="nav-link">Login</a>
                  </li>
                  <li>
                    <a href="/#wordsExperimentsSliderContainerWrapper" class="nav-link">Work</a>
                  </li>
                  <li class="mobile-margin-top-10">
                    <div class="nav-button-wrapper">
                      <div link="#chat" class="btn menu">
                        <div class="button-background menu"></div>
                        <div class="button-text menu">Get started</div>
                        <div class="icon right w-embed">&#xF135;</div>
                      </div>
                    </div>
                  </li>
                </ul>
                <div class="w-embed">
                  <style>
[class*="nav-overlay"]{
    background-color: var(--bg-gauze-heavy);
    background-image: linear-gradient(90deg, var(--bg-gauze) 34%, var(--bg-modern-dark));
    -webkit-backdrop-filter: blur(80px);
    backdrop-filter: blur(80px);
}
</style>
                </div>
              </div>
            </nav>
            <div class="menu-button w-nav-button">
              <div class="icon right icon-menu w-embed">&#xF479;</div>
              <div class="icon right icon-close w-embed">&#xF62A;</div>
              <div class="icon right w-embed">
                <style>
.menu-button[class*="open"] .icon-menu{display:none;}
.menu-button:not([class*="open"]) .icon-close{display:none;}
</style>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content doc-page">
      <h1 class="gradient-text doc-heading">FLAT18.CO.UK Policy on User Privacy</h1>
      <div class="rich-text-block w-richtext">
        <p>This policy applies to ALL services, websites, apps, and projects directly provided by FLAT EIGHTEEN MICROSYSTEMS DEVELOPMENT LLC, (FLAT 18), including testing, beta, and alpha releases unless another data-handling policy exists and explicitly overrides this policy by way of actual declaration. This policy applies to site visitors worldwide.</p>
        <p>This document does not cover third-party services or platforms that may interact with Flat18 systems. Each third party has its own privacy policies, which will apply independently. Where we recognize a conflict between our policy and a third party&#x27;s policy, we may try to inform the user, but we cannot take responsibility for third-party policies.</p>
        <h3>Cookies and Trackers</h3>
        <p>We do not use cookies or trackers to enhance your experience on our main site, flat18.co.uk, except for one exception. We use a cookie to make our website live chat sessions work. The live chat is a self-hosted Chatwoot instance which gathers no user information. On Flat18 projects, hosted, staged, or otherwise executed codebases, we may set cookies for the purpose of account and session management. In such a case, you will be informed of the cookie requirements.</p>
        <h3>Data Collection and Usage</h3>
        <p>Flat18 collects contact information from users during the account creation and usage process. This includes:</p>
        <ul role="list">
          <li><strong>Telegram Handle</strong></li>
          <li><strong>Email Address</strong></li>
          <li><strong>Company or Individual Name</strong></li>
          <li><strong>Physical Address</strong></li>
        </ul>
        <p>We may manually verify this information against a user&#x27;s originating IP geolocation when we believe we have acquired it. However, we can only make educated guesses and assumptions about the IP address matched to the person with whom we are communicating.</p>
        <p>Flat18 does not knowingly utilise any third-party metrics service in any project on our servers. We rely on our own in-house metrics and analysis platform that ensures anonymity by excluding third-party plugins or services. The data derived from our service, which may comprise browser technology, accessing IP addresses, and referral URLs, is not used to identify an individual user.</p>
        <p>Whenever any personal (identifiable) data is stored on our servers, we will take every reasonable precaution to prevent breaches of security or leaking of said data. It is not and will never be our policy to disclose to any third party, the total or any part of the organized data collected on individuals which is either anonymous or identifiable; the only exception being formal, documented requests by police or law officials.</p>
        <h3>Data Retention and Deletion</h3>
        <p>Flat18 aims to retain user information for 2 years. Users may request the deletion of their data from our accountancy system and our communications platform, provided they are not active clients of ours. We will comply with such requests, ensuring that no other personally-identifiable information is kept. However, we will retain records of IP addresses as part of our growing IP geolocation database, which helps us determine that a user has previously made contact with us.</p>
        <h3>Profiling and Analytics</h3>
        <p>Flat18 has identified certain geographical regions from which we encounter a large number of potential scams directed at Flat18. As a result, we actively consider the geographical location of a potential client and may ask additional questions to verify their sincerity. This is our only scope of profiling and does not limit anyone&#x27;s ability to genuinely access our services. This information is never recorded or shared with third parties and is purely an internal practice which is loosely adhered to.</p>
        <h3>Third-Party Payment Processors</h3>
        <p>We use third-party payment processors, such as Stripe and PayPal, for processing fiat-currency transactions. These processors have their own privacy policies, which apply when you visit their domains. It is the user&#x27;s responsibility to research and understand these policies. We are limited in how we portray PayPal and Stripe on our payments pages.</p>
        <h3>User Rights and Requests</h3>
        <p>Users have the right to request the deletion, modification, or access to their data. To make such a request, users should communicate with us from their registered contact method, such as their Telegram handle or email address. We will then verify their identity through follow-up communication, requesting details about their interactions with us, such as the value, currency, and date of their last payment to us. Once verified, we will process the request accordingly.</p>
        <h3>Security Measures</h3>
        <p>Flat18 is committed to protecting user data by employing industry-standard security measures. This includes the use of encryption, firewalls, secure access controls, and regular security audits. We ensure that all personal data is stored securely and that only authorized personnel have access to this information. Our security practices are regularly reviewed and updated to mitigate any potential risks and to comply with industry best practices.</p>
        <p>‍</p>
      </div>
      <div class="badge">Partially Updated: June 2024</div>
    </div>
    <section class="footer-dark">
      <div class="content">
        <div class="footer-wrapper">
          <a href="#" class="footer-brand w-inline-block"><img src="images/logo-24-blue.svg" loading="lazy" alt="" height="30" class="image-4"></a>
          <div class="footer-content">
            <div id="w-node-e28b2c83-3d20-f0ea-2b37-5ec141346fa7-41346f98" class="footer-block">
              <div class="title-small">Contact</div>
              <a link="#chat" href="#" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF24B;</div>
                <div>Live Chat</div>
              </a>
              <a href="https://t.me/flat18_bot" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF5B3;</div>
                <div>Telegram</div>
              </a>
              <a href="mailto:<EMAIL>" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF73B;</div>
                <div><EMAIL></div>
              </a>
              <a href="https://x.com/f18_dev" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF5EF;</div>
                <div>Twitter</div>
              </a>
            </div>
            <div id="w-node-e28b2c83-3d20-f0ea-2b37-5ec141346f9e-41346f98" class="footer-block">
              <div class="title-small">Resources</div>
              <a href="https://stats.uptimerobot.com/RKBX3irMJl" class="footer-link w-inline-block">
                <div class="status good"></div>
                <div>Service status<br></div>
              </a>
              <a href="about.html" class="footer-link">About us</a>
              <a href="ease-of-communication-standard.html" class="footer-link">Standards</a>
              <a href="privacy.html" aria-current="page" class="footer-link w--current">Privacy Policy</a>
              <a href="donate-to-flat18.html" class="footer-link">Donations</a>
              <a href="free-services.html" class="footer-link">Free Services</a>
            </div>
            <div id="w-node-_26beecda-c968-eb58-1bc5-790c38ae2b87-41346f98" class="footer-block">
              <div class="title-small">Shortcuts</div>
              <a href="pricing.html" class="footer-link">Pricing</a>
              <a href="terms.html" class="footer-link">Terms of Service</a>
              <a href="https://accounts.flat18.co.uk/client/login" class="footer-link">Login</a>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-copyright-center">© 2012-<span current-year="true">20</span> FLAT 18</div>
      <div class="code w-embed w-script">
        <script>
for(const year of document.querySelectorAll(`[current-year="true"]`)){
year.innerHTML = new Date().getFullYear()
}
</script>
        <style>
.status.good{
 animation: pulseRing 3s linear forwards infinite;
}
@keyframes pulseRing{
0%{
	box-shadow:0px 0px 0px 0px #00785c;
}
100%{
	box-shadow:0px 0px 0px 5px #00785c00;
}
}
</style>
      </div>
      <div class="w-layout-hflex cookie-notice">
        <div id="w-node-_6c147dfb-07db-e23e-9b2b-165178d9814e-41346f98" class="w-layout-vflex flex-block-2">
          <div class="accept-cookies large-only">This website uses cookies to enhance your browsing experience. Click here to dismiss this notice and accept cookies.<br></div>
          <div class="accept-cookies small-only">We use cookies to enhance your experience.<br>Tap here to accept and close.</div>
          <a href="privacy.html" aria-current="page" class="link cookie w--current">Learn more in our Privacy Policy</a>
        </div>
        <div id="w-node-_6ec3e2e3-cf69-9240-e231-fc58c0b85d27-41346f98" class="icon cookie accept-cookies w-embed">&#xF659;</div>
        <div class="code w-embed w-script">
          <script>
  const userAcknowledgedCookie = localStorage.getItem("userAcceptedCookies") ? localStorage.getItem("userAcceptedCookies") : 'false'
  localStorage.setItem("userAcceptedCookies", userAcknowledgedCookie)
  const bodyClassUserCookies = userAcknowledgedCookie !== 'false' ? 'cookies-accepted':'cookies-not-accepted'
  document.body.classList.add(bodyClassUserCookies)
  document.querySelectorAll(".accept-cookies").forEach((e)=>{
    e.addEventListener("click", ()=>{
      localStorage.setItem("userAcceptedCookies", 'true')
      document.body.classList.add('cookies-accepted')
    })
  })
</script>
          <style>
.cookies-not-accepted .cookie-notice{display: flex;}
.cookies-accepted .cookie-notice{display: none;}
</style>
        </div>
      </div>
    </section>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=663245c7d597883474e88492" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script>
  let theme = localStorage && localStorage.getItem('theme') ? (localStorage.getItem('theme') === 'dark' ? 'auto' : 'light') : 'auto'
  let size = window.outerWidth >= 756 ? "expanded_bubble" : "standard"
  window.chatwootSettings = {
    position: "right", type: size, launcherTitle: "Start here",
    darkMode: theme,
  };
  function initCW(d, t) {
    var BASE_URL = "https://chatwoot.flat18.co.uk";//;"https://app.chatwoot.com"
    var g = d.createElement(t), s = d.getElementsByTagName(t)[0];
    g.src = BASE_URL + "/packs/js/sdk.js";
    g.defer = true;
    g.async = true;
    g.classList.add("chatwoot-script-element")
    s.parentNode.insertBefore(g, s);
    g.onload = function () {
      window.chatwootSDK.run({
        websiteToken: 'krt1otbtLdpkie19rPwPThai',//'jvPpSh5d5zxrQDnanqRYtRx9',
        baseUrl: BASE_URL
      })
    }
  }
  initCW(document, "script")
  function makeid(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() *
        charactersLength));
    }
    return result;
  }
  window.addEventListener("chatwoot:ready", function () {
    // Use window.$chatwoot here
    // ...
    // let random = Date.now() + makeid(8)
    // window.$chatwoot.setUser(random, {
    //   //email: "<<EMAIL>>",
    //   //name: "<name-of-the-user>",
    //   avatar_url: "/src/img/avi.svg",
    //   //phone_number: "<phone-number-of-the-user>",
    // });
    let webMLocal = localStorage && localStorage.getItem("webM") ? localStorage.getItem("webM") : data.webM
    window.$chatwoot.setUser(webMLocal, { name: `${window.geoCityCountry} - ${webMLocal}` });
  });
  function observeParentOpacityChange(targetElement, callback) {
    // Get the grandparent element (parent > parent > parent)
    const grandparentElement = targetElement.parentElement?.parentElement?.parentElement;
    if (!grandparentElement) {
      console.warn('Grandparent element not found.');
      return;
    }
    // Create a callback function to execute when the opacity of the grandparent changes to 1
    function onOpacityChange(mutationsList, observer) {
      for (let mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const currentOpacity = window.getComputedStyle(grandparentElement).opacity;
          if (currentOpacity === '1') {
            // Trigger the provided callback function
            callback();
            // Disconnect the observer
            observer.disconnect();
            console.log('Observer disconnected.');
          }
        }
      }
    }
    // Create a MutationObserver instance and pass in the callback function
    const observer = new MutationObserver(onOpacityChange);
    // Start observing the grandparent element for attribute changes
    observer.observe(grandparentElement, { attributes: true });
  }
  // Original loop, modified to use the observer
  for (const ele of document.querySelectorAll(`[class*="animated-counter"]`)) {
    // Wrap your original setTimeout logic in a function
    const startAnimation = () => {
      let eleCount = 0;
      setTimeout(() => {
        let numb = Number(ele.innerHTML);
        let newNumb = 0;
        setInterval(() => {
          if (newNumb <= numb) {
            ele.innerHTML = newNumb;
            newNumb++;
          }
        }, 1500 / numb);
      }, 200 * eleCount);
      eleCount++;
    };
    // Use the observer to trigger the animation when the grandparent's opacity becomes 1
    observeParentOpacityChange(ele, startAnimation);
  }
  for (const chat of document.querySelectorAll(`[link*="chat"]`)) {
    chat.addEventListener("click", () => {
      window.$chatwoot.toggle("open")
      //Twitter Conversion Event
      twq('event', 'tw-oopi3-ooy6e', {
      });
    })
  }
</script>
</body>
</html>