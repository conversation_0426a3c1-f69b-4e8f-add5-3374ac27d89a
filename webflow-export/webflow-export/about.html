<!DOCTYPE html><!--  Last Published: Sun Dec 22 2024 21:22:50 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="663979de688f1df75e0de222" data-wf-site="663245c7d597883474e88492">
<head>
  <meta charset="utf-8">
  <title>Flat18 — About us</title>
  <meta content="Learn more about Flat 18, a dedicated team specialising in bespoke design and development for websites, landing pages, and Webflow sites. With a commitment to quality, consistency, and innovation, we support startups and small businesses in achieving standout digital experiences tailored for growth." name="description">
  <meta content="Flat18 — About us" property="og:title">
  <meta content="Learn more about Flat 18, a dedicated team specialising in bespoke design and development for websites, landing pages, and Webflow sites. With a commitment to quality, consistency, and innovation, we support startups and small businesses in achieving standout digital experiences tailored for growth." property="og:description">
  <meta content="https://flat18.co.uk/static/advert-flat-18-f18-og_1-p-2000.webp" property="og:image">
  <meta content="Flat18 — About us" property="twitter:title">
  <meta content="Learn more about Flat 18, a dedicated team specialising in bespoke design and development for websites, landing pages, and Webflow sites. With a commitment to quality, consistency, and innovation, we support startups and small businesses in achieving standout digital experiences tailored for growth." property="twitter:description">
  <meta content="https://flat18.co.uk/static/advert-flat-18-f18-og_1-p-2000.webp" property="twitter:image">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/flat18.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <script type="text/javascript">!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.agent='plwebflow';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,document,'script','https://connect.facebook.net/en_US/fbevents.js');fbq('init', '745776286716427');fbq('track', 'PageView');</script>
  <style>
  [class*="btn"]:hover > [class*="button-background"]{
  	transform: translateX(50%);
  }
    [class*="btn"]:hover > [class*="icon"][class*="right"]{
  	transform: translateX(5px);
  }
  .animated-counter::after {
    content: '+';
}
</style>
  <script>
    if(window.location.protocol != 'https:') {
      if(location.href.indexOf("808")<0)
      location.href = location.href.replace("http://", "https://")
    }
    const q = localStorage && localStorage.getItem("webM")? `&webM=${localStorage.getItem("webM")}` : ""
    fetch('https://api.flat18.co.uk/metrics/webm/index.php?geo=1' + q)
      .then(response => response.json())
      .then(data => {
        window.webM = data.webM
        window.geoCityCountry = data.geo
        let persist = localStorage && localStorage.getItem("webM")? localStorage.getItem("webM") : data.webM
        localStorage.setItem("webM", persist)
      });
  </script>
  <script defer="" src="https://eu.umami.is/script.js" data-website-id="54c1aa36-ac18-426d-ba14-3d5827cfa465"></script>
  <script async="" src="https://master--melodic-taffy-1a4c18.netlify.app/tracker.js" data-ackee-server="https://master--melodic-taffy-1a4c18.netlify.app" data-ackee-domain-id="b28e2698-bf04-4e23-9075-a5f7110affe0"></script>
  <!--  Twitter conversion tracking base code  -->
  <script>
!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
twq('config','oopi3');
</script>
  <!--  End Twitter conversion tracking base code  -->
</head>
<body class="body">
  <div class="body-wrapper">
    <div class="navbar-no-shadow">
      <div data-animation="default" data-collapse="medium" data-duration="400" data-easing="ease" data-easing2="ease" role="banner" class="navbar-no-shadow-container w-nav">
        <div class="container-regular">
          <div class="navbar-wrapper">
            <a href="index.html" class="navbar-brand w-nav-brand">
              <div class="homepage-loader"><img src="images/flat18_256x256.avif" loading="lazy" alt="" class="image-11"></div>
              <div class="words-laft-eitheeen">Flat 18</div>
            </a>
            <nav role="navigation" class="nav-menu-wrapper w-nav-menu">
              <div class="div-block">
                <ul role="list" class="nav-menu w-list-unstyled">
                  <li>
                    <a link="#chat" href="#" class="nav-link">Chat</a>
                  </li>
                  <li>
                    <a href="/#pricing" class="nav-link">Pricing</a>
                  </li>
                  <li>
                    <a href="https://accounts.flat18.co.uk/client/login" class="nav-link">Login</a>
                  </li>
                  <li>
                    <a href="/#wordsExperimentsSliderContainerWrapper" class="nav-link">Work</a>
                  </li>
                  <li class="mobile-margin-top-10">
                    <div class="nav-button-wrapper">
                      <div link="#chat" class="btn menu">
                        <div class="button-background menu"></div>
                        <div class="button-text menu">Get started</div>
                        <div class="icon right w-embed">&#xF135;</div>
                      </div>
                    </div>
                  </li>
                </ul>
                <div class="w-embed">
                  <style>
[class*="nav-overlay"]{
    background-color: var(--bg-gauze-heavy);
    background-image: linear-gradient(90deg, var(--bg-gauze) 34%, var(--bg-modern-dark));
    -webkit-backdrop-filter: blur(80px);
    backdrop-filter: blur(80px);
}
</style>
                </div>
              </div>
            </nav>
            <div class="menu-button w-nav-button">
              <div class="icon right icon-menu w-embed">&#xF479;</div>
              <div class="icon right icon-close w-embed">&#xF62A;</div>
              <div class="icon right w-embed">
                <style>
.menu-button[class*="open"] .icon-menu{display:none;}
.menu-button:not([class*="open"]) .icon-close{display:none;}
</style>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content doc-page">
      <h1 class="gradient-text doc-heading">Welcome to Flat 18</h1><img src="images/rotunda3.webp" loading="lazy" width="547" data-w-id="14be043a-6117-a88f-93cd-3f7f74a7d38b" alt="" srcset="images/rotunda3-p-500.webp 500w, images/rotunda3-p-800.webp 800w, images/rotunda3.webp 1000w" sizes="(max-width: 1000px) 100vw, 1000px" class="image-10">
      <div class="paragraph">At Flat 18, we specialise in delivering cutting-edge solutions in the ever-evolving realms of Bitcoin, cryptocurrency, and Web3 technologies. With over a decade of experience, Flat 18 LLC was officially established in 2017, built on a foundation of innovation, technical excellence, and commitment to quality. Our portfolio includes high-impact projects such as BTCPay Server and Wallet Scrutiny—initiatives that contribute meaningfully to the global crypto landscape. Additionally, we’ve developed Web3 solutions for international markets, including exchange and payment utilities for Nigeria’s NairaEx. These are just a few examples of the transformative work we’re proud to lead.<br><br><strong class="h3">A Fearless Approach to Problem Solving</strong><br>At Flat 18, we believe that innovation thrives when fear is removed from the equation. This philosophy drives our approach to every project, whether we’re creating advanced dashboards for veToken or custom payments processors for Bitcoin and Ethereum +ERC-20. We embrace challenges head-on, viewing obstacles as opportunities for growth and refinement. Our development process is agile yet meticulous, allowing us to adapt and respond swiftly while maintaining the high standards that ensure long-term reliability and success.<br><br><strong class="h3">Client-Centred Collaboration</strong><br>Building strong, trusting relationships with our clients is at the heart of Flat 18. Our development process is both collaborative and transparent, providing clients with as much or as little involvement as they prefer. Through the use of best-in-class tools, like Git for version control, we offer a seamless workflow where clients can stay informed and engaged at every step. We pride ourselves on delivering projects that not only meet but exceed expectations, ensuring satisfaction at every critical juncture. Our promise is to continuously refine our work until it reaches a level of perfection our clients expect.<br><br><strong class="h3">Sophisticated and Scalable Solutions</strong><br>We believe in delivering solutions that are both sophisticated and scalable. Our approach leverages the best of modern technologies, from world-class serverless infrastructure to advanced software libraries. We are experts at maximising the efficiency of our builds, creating high-performance systems that are secure, resilient, and capable of scaling to meet your business’s needs. Our curated selection of industry-leading platforms—like GitHub, Vercel, and Neon Postgres—ensures that each project is built on a robust and future-proof foundation, but never at the expense of quality or ambition.<br><br><strong class="h3">What Sets Flat 18 Apart</strong><br>Flat 18 is distinguished by our ability to deliver complex, high-value projects with a focus on craftsmanship and attention to detail. Our clients trust us to handle every element of their project, from the initial concept through to post-launch support, knowing that we’ll deliver exceptional results every time. We specialise in working with cutting-edge technologies, including blockchain and Web3, and are committed to ensuring that every solution we provide is not only innovative but built to the highest industry standards. Our goal is to provide the level of service and expertise that lets our clients feel confident, whether they wish to be deeply involved or leave the finer details to us.<br><br>The future of technology is rich with potential, and Flat 18 is here to help you harness it. <br>Let’s create something extraordinary together. ♥️☘️</div>
    </div>
    <section class="footer-dark">
      <div class="content">
        <div class="footer-wrapper">
          <a href="#" class="footer-brand w-inline-block"><img src="images/logo-24-blue.svg" loading="lazy" alt="" height="30" class="image-4"></a>
          <div class="footer-content">
            <div id="w-node-e28b2c83-3d20-f0ea-2b37-5ec141346fa7-41346f98" class="footer-block">
              <div class="title-small">Contact</div>
              <a link="#chat" href="#" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF24B;</div>
                <div>Live Chat</div>
              </a>
              <a href="https://t.me/flat18_bot" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF5B3;</div>
                <div>Telegram</div>
              </a>
              <a href="mailto:<EMAIL>" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF73B;</div>
                <div><EMAIL></div>
              </a>
              <a href="https://x.com/f18_dev" class="footer-link w-inline-block">
                <div class="icon small x-small w-embed">&#xF5EF;</div>
                <div>Twitter</div>
              </a>
            </div>
            <div id="w-node-e28b2c83-3d20-f0ea-2b37-5ec141346f9e-41346f98" class="footer-block">
              <div class="title-small">Resources</div>
              <a href="https://stats.uptimerobot.com/RKBX3irMJl" class="footer-link w-inline-block">
                <div class="status good"></div>
                <div>Service status<br></div>
              </a>
              <a href="about.html" aria-current="page" class="footer-link w--current">About us</a>
              <a href="ease-of-communication-standard.html" class="footer-link">Standards</a>
              <a href="privacy.html" class="footer-link">Privacy Policy</a>
              <a href="donate-to-flat18.html" class="footer-link">Donations</a>
              <a href="free-services.html" class="footer-link">Free Services</a>
            </div>
            <div id="w-node-_26beecda-c968-eb58-1bc5-790c38ae2b87-41346f98" class="footer-block">
              <div class="title-small">Shortcuts</div>
              <a href="pricing.html" class="footer-link">Pricing</a>
              <a href="terms.html" class="footer-link">Terms of Service</a>
              <a href="https://accounts.flat18.co.uk/client/login" class="footer-link">Login</a>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-copyright-center">© 2012-<span current-year="true">20</span> FLAT 18</div>
      <div class="code w-embed w-script">
        <script>
for(const year of document.querySelectorAll(`[current-year="true"]`)){
year.innerHTML = new Date().getFullYear()
}
</script>
        <style>
.status.good{
 animation: pulseRing 3s linear forwards infinite;
}
@keyframes pulseRing{
0%{
	box-shadow:0px 0px 0px 0px #00785c;
}
100%{
	box-shadow:0px 0px 0px 5px #00785c00;
}
}
</style>
      </div>
      <div class="w-layout-hflex cookie-notice">
        <div id="w-node-_6c147dfb-07db-e23e-9b2b-165178d9814e-41346f98" class="w-layout-vflex flex-block-2">
          <div class="accept-cookies large-only">This website uses cookies to enhance your browsing experience. Click here to dismiss this notice and accept cookies.<br></div>
          <div class="accept-cookies small-only">We use cookies to enhance your experience.<br>Tap here to accept and close.</div>
          <a href="privacy.html" class="link cookie">Learn more in our Privacy Policy</a>
        </div>
        <div id="w-node-_6ec3e2e3-cf69-9240-e231-fc58c0b85d27-41346f98" class="icon cookie accept-cookies w-embed">&#xF659;</div>
        <div class="code w-embed w-script">
          <script>
  const userAcknowledgedCookie = localStorage.getItem("userAcceptedCookies") ? localStorage.getItem("userAcceptedCookies") : 'false'
  localStorage.setItem("userAcceptedCookies", userAcknowledgedCookie)
  const bodyClassUserCookies = userAcknowledgedCookie !== 'false' ? 'cookies-accepted':'cookies-not-accepted'
  document.body.classList.add(bodyClassUserCookies)
  document.querySelectorAll(".accept-cookies").forEach((e)=>{
    e.addEventListener("click", ()=>{
      localStorage.setItem("userAcceptedCookies", 'true')
      document.body.classList.add('cookies-accepted')
    })
  })
</script>
          <style>
.cookies-not-accepted .cookie-notice{display: flex;}
.cookies-accepted .cookie-notice{display: none;}
</style>
        </div>
      </div>
    </section>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=663245c7d597883474e88492" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script>
  let theme = localStorage && localStorage.getItem('theme') ? (localStorage.getItem('theme') === 'dark' ? 'auto' : 'light') : 'auto'
  let size = window.outerWidth >= 756 ? "expanded_bubble" : "standard"
  window.chatwootSettings = {
    position: "right", type: size, launcherTitle: "Start here",
    darkMode: theme,
  };
  function initCW(d, t) {
    var BASE_URL = "https://chatwoot.flat18.co.uk";//;"https://app.chatwoot.com"
    var g = d.createElement(t), s = d.getElementsByTagName(t)[0];
    g.src = BASE_URL + "/packs/js/sdk.js";
    g.defer = true;
    g.async = true;
    g.classList.add("chatwoot-script-element")
    s.parentNode.insertBefore(g, s);
    g.onload = function () {
      window.chatwootSDK.run({
        websiteToken: 'krt1otbtLdpkie19rPwPThai',//'jvPpSh5d5zxrQDnanqRYtRx9',
        baseUrl: BASE_URL
      })
    }
  }
  initCW(document, "script")
  function makeid(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() *
        charactersLength));
    }
    return result;
  }
  window.addEventListener("chatwoot:ready", function () {
    // Use window.$chatwoot here
    // ...
    // let random = Date.now() + makeid(8)
    // window.$chatwoot.setUser(random, {
    //   //email: "<<EMAIL>>",
    //   //name: "<name-of-the-user>",
    //   avatar_url: "/src/img/avi.svg",
    //   //phone_number: "<phone-number-of-the-user>",
    // });
    let webMLocal = localStorage && localStorage.getItem("webM") ? localStorage.getItem("webM") : data.webM
    window.$chatwoot.setUser(webMLocal, { name: `${window.geoCityCountry} - ${webMLocal}` });
  });
  function observeParentOpacityChange(targetElement, callback) {
    // Get the grandparent element (parent > parent > parent)
    const grandparentElement = targetElement.parentElement?.parentElement?.parentElement;
    if (!grandparentElement) {
      console.warn('Grandparent element not found.');
      return;
    }
    // Create a callback function to execute when the opacity of the grandparent changes to 1
    function onOpacityChange(mutationsList, observer) {
      for (let mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const currentOpacity = window.getComputedStyle(grandparentElement).opacity;
          if (currentOpacity === '1') {
            // Trigger the provided callback function
            callback();
            // Disconnect the observer
            observer.disconnect();
            console.log('Observer disconnected.');
          }
        }
      }
    }
    // Create a MutationObserver instance and pass in the callback function
    const observer = new MutationObserver(onOpacityChange);
    // Start observing the grandparent element for attribute changes
    observer.observe(grandparentElement, { attributes: true });
  }
  // Original loop, modified to use the observer
  for (const ele of document.querySelectorAll(`[class*="animated-counter"]`)) {
    // Wrap your original setTimeout logic in a function
    const startAnimation = () => {
      let eleCount = 0;
      setTimeout(() => {
        let numb = Number(ele.innerHTML);
        let newNumb = 0;
        setInterval(() => {
          if (newNumb <= numb) {
            ele.innerHTML = newNumb;
            newNumb++;
          }
        }, 1500 / numb);
      }, 200 * eleCount);
      eleCount++;
    };
    // Use the observer to trigger the animation when the grandparent's opacity becomes 1
    observeParentOpacityChange(ele, startAnimation);
  }
  for (const chat of document.querySelectorAll(`[link*="chat"]`)) {
    chat.addEventListener("click", () => {
      window.$chatwoot.toggle("open")
      //Twitter Conversion Event
      twq('event', 'tw-oopi3-ooy6e', {
      });
    })
  }
</script>
</body>
</html>