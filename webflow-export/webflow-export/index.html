<!DOCTYPE html><!--  Last Published: Sun Dec 22 2024 21:22:50 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="663245c7d597883474e8849f" data-wf-site="663245c7d597883474e88492">
<head>
  <meta charset="utf-8">
  <title>Flat 18 — Bespoke Design &amp; Development for Landing Pages, Websites, and Webflow Sites</title>
  <meta content="Flat 18 offers expert design and development services for landing pages, websites, and Webflow sites, tailored for startups, small businesses, and entrepreneurs. Our subscription model provides consistent, high-quality support to create standout digital experiences that drive growth and engagement." name="description">
  <meta content="Flat 18 — Bespoke Design &amp; Development for Landing Pages, Websites, and Webflow Sites" property="og:title">
  <meta content="Flat 18 offers expert design and development services for landing pages, websites, and Webflow sites, tailored for startups, small businesses, and entrepreneurs. Our subscription model provides consistent, high-quality support to create standout digital experiences that drive growth and engagement." property="og:description">
  <meta content="https://flat18.co.uk/static/advert-flat-18-f18-og_1-p-2000.webp" property="og:image">
  <meta content="Flat 18 — Bespoke Design &amp; Development for Landing Pages, Websites, and Webflow Sites" property="twitter:title">
  <meta content="Flat 18 offers expert design and development services for landing pages, websites, and Webflow sites, tailored for startups, small businesses, and entrepreneurs. Our subscription model provides consistent, high-quality support to create standout digital experiences that drive growth and engagement." property="twitter:description">
  <meta content="https://flat18.co.uk/static/advert-flat-18-f18-og_1-p-2000.webp" property="twitter:image">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/flat18.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <script type="text/javascript">!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.agent='plwebflow';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,document,'script','https://connect.facebook.net/en_US/fbevents.js');fbq('init', '745776286716427');fbq('track', 'PageView');</script>
  <style>
  [class*="btn"]:hover > [class*="button-background"]{
  	transform: translateX(50%);
  }
    [class*="btn"]:hover > [class*="icon"][class*="right"]{
  	transform: translateX(5px);
  }
  .animated-counter::after {
    content: '+';
}
</style>
  <script>
    if(window.location.protocol != 'https:') {
      if(location.href.indexOf("808")<0)
      location.href = location.href.replace("http://", "https://")
    }
    const q = localStorage && localStorage.getItem("webM")? `&webM=${localStorage.getItem("webM")}` : ""
    fetch('https://api.flat18.co.uk/metrics/webm/index.php?geo=1' + q)
      .then(response => response.json())
      .then(data => {
        window.webM = data.webM
        window.geoCityCountry = data.geo
        let persist = localStorage && localStorage.getItem("webM")? localStorage.getItem("webM") : data.webM
        localStorage.setItem("webM", persist)
      });
  </script>
  <script defer="" src="https://eu.umami.is/script.js" data-website-id="54c1aa36-ac18-426d-ba14-3d5827cfa465"></script>
  <script async="" src="https://master--melodic-taffy-1a4c18.netlify.app/tracker.js" data-ackee-server="https://master--melodic-taffy-1a4c18.netlify.app" data-ackee-domain-id="b28e2698-bf04-4e23-9075-a5f7110affe0"></script>
  <!--  Twitter conversion tracking base code  -->
  <script>
!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
twq('config','oopi3');
</script>
  <!--  End Twitter conversion tracking base code  -->
</head>
<body class="body">
  <div class="body-wrapper no-overflow">
    <div class="navbar-no-shadow">
      <div data-animation="default" data-collapse="medium" data-duration="400" data-easing="ease" data-easing2="ease" role="banner" class="navbar-no-shadow-container w-nav">
        <div class="container-regular">
          <div class="navbar-wrapper">
            <a href="index.html" aria-current="page" class="navbar-brand w-nav-brand w--current">
              <div class="homepage-loader"><img src="images/flat18_256x256.avif" loading="lazy" alt="" class="image-11"></div>
              <div class="words-laft-eitheeen">Flat 18</div>
            </a>
            <nav role="navigation" class="nav-menu-wrapper w-nav-menu">
              <div class="div-block">
                <ul role="list" class="nav-menu w-list-unstyled">
                  <li>
                    <a link="#chat" href="#" class="nav-link">Chat</a>
                  </li>
                  <li>
                    <a href="/#pricing" class="nav-link">Pricing</a>
                  </li>
                  <li>
                    <a href="https://accounts.flat18.co.uk/client/login" class="nav-link">Login</a>
                  </li>
                  <li>
                    <a href="/#wordsExperimentsSliderContainerWrapper" class="nav-link">Work</a>
                  </li>
                  <li class="mobile-margin-top-10">
                    <div class="nav-button-wrapper">
                      <div link="#chat" class="btn menu">
                        <div class="button-background menu"></div>
                        <div class="button-text menu">Get started</div>
                        <div class="icon right w-embed">&#xF135;</div>
                      </div>
                    </div>
                  </li>
                </ul>
                <div class="w-embed">
                  <style>
[class*="nav-overlay"]{
    background-color: var(--bg-gauze-heavy);
    background-image: linear-gradient(90deg, var(--bg-gauze) 34%, var(--bg-modern-dark));
    -webkit-backdrop-filter: blur(80px);
    backdrop-filter: blur(80px);
}
</style>
                </div>
              </div>
            </nav>
            <div class="menu-button w-nav-button">
              <div class="icon right icon-menu w-embed">&#xF479;</div>
              <div class="icon right icon-close w-embed">&#xF62A;</div>
              <div class="icon right w-embed">
                <style>
.menu-button[class*="open"] .icon-menu{display:none;}
.menu-button:not([class*="open"]) .icon-close{display:none;}
</style>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="body-contents-wrapper">
      <div class="content hero">
        <div class="content-video-wrapper hero">
          <div class="hero-animation-wrapper">
            <div class="hero-background-video">
              <div class="animation-segment top"></div>
              <div class="animation-segment left"></div>
              <div class="animation-segment right"></div>
              <div class="animation-segment bottom"></div>
              <div id="w-node-_6dc4ceeb-6a38-d0e4-cb8e-508959156b5c-404d75c2" class="w-embed">
                <style>
:root {
--hero-speed1:30s;
}
.hero-background-video{
transform-style: preserve-3d;
transform: translate(-50%,-50%) perspective(.75cm);
}
.hero-animation-wrapper{
animation: rotate-bg 100s linear infinite;
}
.animation-segment.left,.animation-segment.right {
animation: warps var(--hero-speed1) linear infinite;
}
.animation-segment.top {
animation: warp-covers var(--hero-speed1) linear infinite;
}
.animation-segment.bottom {
animation: warp-covers var(--hero-speed1) linear reverse infinite;
}
@keyframes warps {
  0% {
    background-position-x:0px;
  }
  100% {
    background-position-x: -400px;
  }
}
@keyframes warp-covers {
  0% {
    background-position-y:0;
  }
  100% {
    background-position-y: 225px;
  }
}
@keyframes rotate-bg {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
              </div>
            </div>
          </div>
        </div>
        <div id="w-node-_8df47551-d3a1-31bc-10bd-8c3b404d75c3-404d75c2" class="w-layout-vflex hero-text-cta">
          <div class="btn-flex">
            <a href="#faq" class="limited-availability-prompt w-inline-block">
              <div class="badge inline-badge">
                <div class="status good"></div>
                <div class="text-block-6">Open for New Projects</div>
                <div class="icon right in-badge w-embed">&#xF431;</div>
              </div>
            </a>
          </div>
          <div class="hero-heading-wrapper">
            <h1 class="hero-heading animation-target">We Craft Websites &amp; Apps</h1>
            <div class="hero-heading invisible">Human-made Websites &amp; Apps</div>
          </div>
          <h2 class="hero-h2">Full-service Design &amp; Development solutions for <span class="text-span-3">startups</span> Everyone!<br></h2>
          <div class="btn-flex">
            <aside link="#chat" class="btn hero">
              <div class="button-background"></div>
              <div class="button-text">Start Your Project</div>
              <div class="icon right small full-opa w-embed">&#xF6B9;</div>
            </aside>
          </div>
          <div class="code w-embed w-script">
            <script>
    try { document.querySelector(".hero-heading.animation-target").style.color = 'transparent' } catch (e) { }
    requestAnimationFrame(() => {
      const heroHeading = document.querySelector(".hero-heading.animation-target");
      if (heroHeading) {
        heroHeading.style['min-height'] = heroHeading.getBoundingClientRect().height + 'px'
        const targetText = heroHeading.textContent.trim(); // Trim any extra whitespace
        const words = targetText.split(" "); // Split the text into an array of words
        let wordIndex = 0;
        let charIndex = 0;
        let fullCharIndex = 0;
        heroHeading.innerHTML = "";
        heroHeading.setAttribute("aria-live", "polite");
        const typeWord = () => {
          if (wordIndex < words.length) {
            // Create a container for the current word
            let wordContainer;
            // If starting a new word, create a word container
            if (charIndex === 0) {
              wordContainer = document.createElement("span"); // You can replace <span> with any tag, like <div> or <p>
              wordContainer.classList.add("word"); // Add a class for the word container
              heroHeading.append(wordContainer);  // Append the word container to heroHeading
            } else {
              wordContainer = heroHeading.querySelectorAll(".word")[wordIndex]; // Find the existing word container
            }
            // Get the current word and character
            const currentWord = words[wordIndex];
            // If not all characters of the current word are typed, keep typing characters
            if (charIndex < currentWord.length) {
              let newCharacter = document.createElement("span");
             // newCharacter.style.color = gradColours[Math.ceil(fullCharIndex / 3)];
              newCharacter.classList.add("character"); // Add the character class to each character
              newCharacter.innerHTML = currentWord.charAt(charIndex).replace(/ /g, '&nbsp;');
              wordContainer.append(newCharacter);
              charIndex++;
              // Continue typing the next character in the current word
              setTimeout(() => {
                requestAnimationFrame(typeWord);
              }, 50); // Delay between characters within the word
            } else {
              // After finishing the current word, move to the next word
              charIndex = 0; // Reset character index for the next word
              wordIndex++;
              // Delay before starting to type the next word
              // setTimeout(() => {
              requestAnimationFrame(typeWord);
              //}, 10); // Delay between words
            }
            fullCharIndex++
          }
        };
        // Start typing after a slight delay
        setTimeout(() => {
          requestAnimationFrame(typeWord);
        }, 10);
      }
    });
  </script>
            <style>
    .hero-heading .word {
      color: var(--primary);
      opacity: 0;
      position: relative;
      animation: characterSlideUp .5s ease forwards 1;
      filter: unset !important;
    }
    .hero-heading .character {
      opacity: 0;
      position: relative;
      animation: characterSlideUp .5s ease forwards 1;
    }
    @keyframes characterSlideUp {
      from {
        opacity: 0;
        transform: translate(10px, 0em)scale(1.1);
        filter: blur(100px);
      }
      to {
        opacity: 1;
        transform: translate(0px, 0em)scale(1);
        filter: blur(0px);
      }
    }
  </style>
          </div>
        </div>
      </div>
      <div class="content tools-wrapper">
        <div class="tile-flex-animated-container">
          <div class="w-layout-hflex provider-image-container"></div>
          <div class="providers-overlay right"></div>
          <div class="providers-overlay"></div>
        </div>
        <div class="html-embed-2 w-embed">
          <style>
[class*="provider-image-container"]{
  animation: loopProviderFlex 60s linear forwards infinite
}
@keyframes loopProviderFlex {
  0% {
    transform: translateX(-25%);
  }
  100% {
    transform: translateX(-75%);
  }
}
</style>
        </div>
      </div>
      <div data-w-id="de05dcd5-17ab-68a4-a864-3e2f46416da3" class="w-layout-vflex content primere-btf-content content-build-in-target">
        <div class="w-layout-vflex text-org how-it-works">
          <h2 class="gradient-text yellow">The Flat 18 Way:</h2>
        </div>
        <div class="content sliding-work-showcase highlight-subtle-block how-it-works">
          <div class="work-tile wide-work-tile transparent">
            <div class="w-layout-hflex work-tile-text">
              <div class="w-layout-hflex flex-header-tile">
                <h3 class="work-ile-title numbering-title">01 <span class="subtlise">Discover</span></h3>
              </div>
              <div class="animation-wrapper framed">
                <div class="how-it-works-video step1-animation-container w-embed"><svg width="100%" height="100%" viewbox="0 0 1121 1121" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:square;stroke-miterlimit:1.5;">
                    <rect id="Artboard9" x="0" y="0" width="1120.39" height="1120.39" style="fill:none;"></rect>
                    <clippath id="_dia_ui_clip1">
                      <rect x="0" y="0" width="1120.39" height="1120.39"></rect>
                    </clippath>
                    <g clip-path="url(#_dia_ui_clip1)">
                      <ellipse cx="560.196" cy="627.22" rx="523.144" ry="529.423" style="fill:url(#_Radial2);"></ellipse>
                      <path d="M896.895,152.341L896.895,1152.64C896.895,1208.39 851.635,1253.65 795.888,1253.65L324.505,1253.65C268.758,1253.65 223.498,1208.39 223.498,1152.64L223.498,152.341C223.498,96.594 268.758,51.334 324.505,51.334L795.888,51.334C851.635,51.334 896.895,96.594 896.895,152.341ZM884.395,152.341C884.395,103.493 844.736,63.834 795.888,63.834L324.505,63.834C275.657,63.834 235.998,103.493 235.998,152.341L235.998,1152.64C235.998,1201.49 275.657,1241.15 324.505,1241.15L795.888,1241.15C844.736,1241.15 884.395,1201.49 884.395,1152.64L884.395,152.341Z" style="fill:url(#_dia_ui_Linear3);"></path>
                      <path d="M890.645,152.341L890.645,1152.64C890.645,1204.94 848.186,1247.4 795.888,1247.4L324.505,1247.4C272.207,1247.4 229.748,1204.94 229.748,1152.64L229.748,152.341C229.748,100.043 272.207,57.584 324.505,57.584L795.888,57.584C848.186,57.584 890.645,100.043 890.645,152.341Z" style="fill:url(#_Radial4);"></path>
                      <path d="M641.039,119.058C641.039,130.792 631.512,140.319 619.778,140.319L500.615,140.319C488.881,140.319 479.354,130.792 479.354,119.058C479.354,107.323 488.881,97.797 500.615,97.797L619.778,97.797C631.512,97.797 641.039,107.323 641.039,119.058Z"></path>
                      <g id="initial-message-group">
                        <path id="user-chat-bubble" d="M1017.62,367.033L1017.62,561.839C1017.62,575.765 1006.31,587.072 992.383,587.072L498.816,587.072C484.889,587.072 473.582,575.765 473.582,561.839L473.582,367.033C473.582,353.106 484.889,341.799 498.816,341.799L992.383,341.799C1006.31,341.799 1017.62,353.106 1017.62,367.033Z" style="fill:rgb(13,40,242);"></path>
                        <clippath id="_dia_ui_clip5">
                          <path d="M1017.62,367.033L1017.62,561.839C1017.62,575.765 1006.31,587.072 992.383,587.072L498.816,587.072C484.889,587.072 473.582,575.765 473.582,561.839L473.582,367.033C473.582,353.106 484.889,341.799 498.816,341.799L992.383,341.799C1006.31,341.799 1017.62,353.106 1017.62,367.033Z"></path>
                        </clippath>
                        <g clip-path="url(#_dia_ui_clip5)">
                          <g transform="matrix(1,0,0,1,-5939.5,-250.526)">
                            <text x="6471.04px" y="668.546px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:45.833px;fill:white;">Hey<tspan x="6549.14px 6561.88px " y="668.546px 668.546px ">, </tspan>Flat 18</text>
                            <text x="6471.04px" y="726.879px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:45.833px;fill:white;">I&apos;ve got an amazing</text>
                            <text x="6471.04px" y="785.212px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:45.833px;fill:white;">idea for an app!</text>
                            <text x="6802.3px" y="785.212px" style="font-family:'AppleColorEmoji', 'Apple Color Emoji';font-size:45.833px;fill:white;">💡</text>
                            <text x="6860.87px" y="785.212px" style="font-family:'AppleColorEmoji', 'Apple Color Emoji';font-size:45.833px;fill:white;">🤩</text>
                          </g>
                        </g>
                        <g id="read-status">
                          <path d="M844.457,605.215L830.376,624.184L822.372,618.32" style="fill:none;stroke:rgb(13,228,117);stroke-width:2.95px;"></path>
                          <path d="M854.129,605.215L840.048,624.184L838.107,622.762" style="fill:none;stroke:rgb(13,228,117);stroke-width:2.95px;"></path>
                          <g transform="matrix(26.5673,0,0,26.5673,803.257,623.892)">
                          </g>
                          <text x="750.11px" y="623.892px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:26.567px;fill:rgb(151,151,151);">Now</text>
                        </g>
                      </g>
                      <g id="response-group">
                        <path id="response-chat-bubble" d="M712.076,759.49L712.076,891.212C712.076,905.138 700.769,916.445 686.843,916.445L122.315,916.445C108.388,916.445 97.082,905.138 97.082,891.212L97.082,759.49C97.082,745.563 108.388,734.256 122.315,734.256L686.843,734.256C700.769,734.256 712.076,745.563 712.076,759.49Z" style="fill:white;"></path>
                        <clippath id="_dia_ui_clip6">
                          <path d="M712.076,759.49L712.076,891.212C712.076,905.138 700.769,916.445 686.843,916.445L122.315,916.445C108.388,916.445 97.082,905.138 97.082,891.212L97.082,759.49C97.082,745.563 108.388,734.256 122.315,734.256L686.843,734.256C700.769,734.256 712.076,745.563 712.076,759.49Z"></path>
                        </clippath>
                        <g clip-path="url(#_dia_ui_clip6)">
                          <g transform="matrix(1,0,0,1,-6319.24,141.862)">
                            <text x="6471.04px" y="668.546px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:45.833px;">Brilliant!</text>
                            <text x="6471.04px" y="726.879px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:45.833px;">Let&apos;s make it happen</text>
                            <g transform="matrix(45.8333,0,0,45.8333,6996.96,726.879)">
                            </g>
                            <text x="6905.29px" y="726.879px" style="font-family:'AppleColorEmoji', 'Apple Color Emoji';font-size:45.833px;">⚡️⚡️</text>
                          </g>
                        </g>
                        <g id="read-status1" serif:id="read-status">
                          <path d="M731.013,784.024C728.776,784.346 726.49,784.512 724.166,784.512C697.799,784.512 676.392,763.106 676.392,736.739C676.392,710.372 697.799,688.965 724.166,688.965C750.533,688.965 771.939,710.372 771.939,736.739C771.939,743.124 770.684,749.219 768.406,754.79C770.646,758.427 771.939,762.709 771.939,767.291C771.939,780.475 761.236,791.178 748.053,791.178C741.383,791.178 735.348,788.438 731.013,784.024ZM771.939,790.131C775.515,790.131 778.418,793.034 778.418,796.61C778.418,800.185 775.515,803.088 771.939,803.088C768.364,803.088 765.461,800.185 765.461,796.61C765.461,793.034 768.364,790.131 771.939,790.131Z" style="fill:rgb(13,40,242);"></path>
                          <path d="M699.949,738.945C696.752,736.045 694.744,731.859 694.744,727.207C694.744,718.462 701.843,711.362 710.588,711.362C716.449,711.362 721.571,714.551 724.312,719.287C727.053,714.551 732.174,711.362 738.035,711.362C746.78,711.362 753.88,718.462 753.88,727.207C753.88,731.859 751.871,736.045 748.674,738.945L724.312,763.307L699.949,738.945Z" style="fill:rgb(242,13,94);"></path>
                        </g>
                      </g>
                      <g id="typing-indicator">
                        <path d="M436.71,734.18C436.71,748.646 424.965,760.392 410.499,760.392L329.94,760.392C315.473,760.392 303.728,748.646 303.728,734.18C303.728,719.713 315.473,707.968 329.94,707.968L410.499,707.968C424.965,707.968 436.71,719.713 436.71,734.18Z" style="fill:rgb(240,255,248);"></path>
                        <circle cx="370.219" cy="734.18" r="12.448" style="fill:rgb(161,181,172);"></circle>
                        <circle cx="329.899" cy="734.18" r="12.448" style="fill:rgb(161,181,172);"></circle>
                        <circle cx="410.539" cy="734.18" r="12.448" style="fill:rgb(161,181,172);"></circle>
                      </g>
                      <g id="chat-title">
                        <g>
                          <circle cx="320.31" cy="183.638" r="34.089" style="fill:rgb(8,79,155);"></circle>
                          <path d="M317.889,149.634C318.689,149.578 319.496,149.549 320.31,149.549C324.052,149.549 327.653,150.153 331.022,151.269C333.443,152.071 335.744,153.137 337.891,154.433L337.417,161.8L332.217,184.886L337.417,205.108L337.417,213.124C335.351,214.326 333.147,215.318 330.836,216.068C327.52,217.145 323.983,217.727 320.31,217.727C316.776,217.727 313.366,217.188 310.159,216.187L310.159,177.229L301.561,183.154L291.863,168.088L317.889,149.634Z" style="fill:rgb(85,163,246);"></path>
                          <path d="M331.022,151.269C333.443,152.071 335.744,153.137 337.891,154.433C347.781,160.403 354.399,171.254 354.399,183.638C354.399,196.219 347.569,207.218 337.417,213.124C335.351,214.326 333.147,215.318 330.836,216.068C328.826,215.345 327.001,214.348 325.363,213.079C323.245,211.438 321.527,209.326 320.21,206.744C318.893,204.161 318.234,201.047 318.234,197.401C318.234,193.877 319.074,190.839 320.753,188.287C322.431,185.735 324.64,183.79 327.377,182.453L327.377,182.18C325.104,181.147 323.167,179.461 321.566,177.121C319.965,174.782 319.164,171.941 319.164,168.599C319.164,165.257 319.784,162.371 321.024,159.94C322.263,157.51 323.904,155.535 325.944,154.016C327.47,152.879 329.163,151.964 331.022,151.269Z" style="fill:rgb(240,255,249);"></path>
                        </g>
                        <g>
                          <path d="M379.486,203.212L379.486,163.35L406.378,163.35L406.378,168.054L384.761,168.054L384.761,180.399L403.469,180.399L403.469,185.103L384.761,185.103L384.761,203.212L379.486,203.212Z" style="fill:rgb(85,163,246);fill-rule:nonzero;"></path>
                          <rect x="412.496" y="163.35" width="4.894" height="39.862" style="fill:rgb(85,163,246);fill-rule:nonzero;"></rect>
                          <path d="M443.82,199.65C442.008,201.191 440.263,202.279 438.586,202.913C436.909,203.548 435.11,203.865 433.189,203.865C430.016,203.865 427.578,203.09 425.874,201.54C424.17,199.99 423.318,198.01 423.318,195.599C423.318,194.185 423.64,192.893 424.284,191.724C424.927,190.555 425.77,189.617 426.812,188.91C427.855,188.203 429.028,187.668 430.334,187.306C431.294,187.052 432.745,186.807 434.684,186.571C438.636,186.1 441.545,185.538 443.413,184.885C443.431,184.215 443.44,183.789 443.44,183.608C443.44,181.614 442.977,180.209 442.053,179.393C440.802,178.287 438.944,177.734 436.479,177.734C434.177,177.734 432.477,178.138 431.38,178.944C430.284,179.751 429.473,181.178 428.947,183.227L424.161,182.574C424.596,180.526 425.312,178.872 426.309,177.612C427.306,176.352 428.747,175.382 430.633,174.702C432.518,174.023 434.702,173.683 437.186,173.683C439.651,173.683 441.654,173.973 443.195,174.553C444.736,175.133 445.869,175.863 446.594,176.742C447.319,177.621 447.827,178.731 448.117,180.073C448.28,180.907 448.361,182.411 448.361,184.586L448.361,191.112C448.361,195.662 448.466,198.54 448.674,199.745C448.882,200.951 449.295,202.107 449.911,203.212L444.799,203.212C444.292,202.197 443.965,201.01 443.82,199.65ZM443.413,188.719C441.636,189.445 438.971,190.061 435.418,190.568C433.406,190.858 431.983,191.185 431.149,191.547C430.315,191.91 429.672,192.44 429.219,193.138C428.766,193.836 428.539,194.611 428.539,195.463C428.539,196.768 429.033,197.856 430.021,198.726C431.009,199.596 432.455,200.031 434.358,200.031C436.243,200.031 437.92,199.619 439.388,198.794C440.857,197.969 441.935,196.841 442.624,195.408C443.15,194.303 443.413,192.671 443.413,190.514L443.413,188.719Z" style="fill:rgb(85,163,246);fill-rule:nonzero;"></path>
                          <path d="M466.634,198.835L467.341,203.158C465.963,203.448 464.73,203.593 463.643,203.593C461.866,203.593 460.489,203.312 459.51,202.75C458.531,202.188 457.842,201.449 457.443,200.534C457.044,199.619 456.845,197.693 456.845,194.756L456.845,178.142L453.256,178.142L453.256,174.335L456.845,174.335L456.845,167.184L461.712,164.247L461.712,174.335L466.634,174.335L466.634,178.142L461.712,178.142L461.712,195.028C461.712,196.424 461.798,197.321 461.97,197.72C462.143,198.119 462.424,198.436 462.813,198.671C463.203,198.907 463.761,199.025 464.486,199.025C465.029,199.025 465.745,198.961 466.634,198.835Z" style="fill:rgb(85,163,246);fill-rule:nonzero;"></path>
                          <path d="M503.967,203.212L499.073,203.212L499.073,172.024C497.894,173.148 496.349,174.272 494.437,175.396C492.524,176.52 490.807,177.363 489.284,177.925L489.284,173.193C492.021,171.906 494.414,170.347 496.462,168.516C498.511,166.686 499.961,164.909 500.813,163.187L503.967,163.187L503.967,203.212Z" style="fill:rgb(85,163,246);fill-rule:nonzero;"></path>
                          <path d="M524.034,181.595C522.004,180.852 520.499,179.792 519.52,178.414C518.542,177.036 518.052,175.387 518.052,173.465C518.052,170.565 519.094,168.127 521.179,166.151C523.264,164.175 526.037,163.187 529.5,163.187C532.98,163.187 535.781,164.198 537.902,166.219C540.023,168.24 541.083,170.701 541.083,173.601C541.083,175.45 540.598,177.059 539.628,178.428C538.658,179.796 537.186,180.852 535.21,181.595C537.657,182.393 539.52,183.68 540.798,185.457C542.076,187.233 542.715,189.354 542.715,191.819C542.715,195.227 541.509,198.091 539.098,200.412C536.687,202.732 533.515,203.892 529.581,203.892C525.648,203.892 522.475,202.727 520.064,200.398C517.653,198.069 516.448,195.164 516.448,191.683C516.448,189.091 517.105,186.92 518.419,185.171C519.733,183.422 521.605,182.23 524.034,181.595ZM523.055,173.302C523.055,175.187 523.663,176.728 524.877,177.925C526.092,179.121 527.669,179.719 529.608,179.719C531.494,179.719 533.039,179.126 534.244,177.938C535.45,176.751 536.053,175.296 536.053,173.574C536.053,171.779 535.432,170.27 534.19,169.047C532.948,167.823 531.403,167.211 529.554,167.211C527.687,167.211 526.137,167.809 524.904,169.006C523.672,170.202 523.055,171.634 523.055,173.302ZM521.478,191.71C521.478,193.106 521.809,194.457 522.471,195.762C523.132,197.067 524.116,198.078 525.421,198.794C526.726,199.51 528.131,199.868 529.636,199.868C531.974,199.868 533.905,199.116 535.427,197.611C536.95,196.106 537.711,194.194 537.711,191.874C537.711,189.517 536.927,187.568 535.359,186.028C533.791,184.487 531.829,183.716 529.472,183.716C527.17,183.716 525.262,184.478 523.749,186C522.235,187.523 521.478,189.426 521.478,191.71Z" style="fill:rgb(85,163,246);fill-rule:nonzero;"></path>
                        </g>
                      </g>
                    </g>
                    <defs>
                      <radialgradient id="_Radial2" cx="0" cy="0" r="1" gradientunits="userSpaceOnUse" gradienttransform="matrix(523.144,0,0,560.196,560.196,593.957)">
                        <stop offset="0" style="stop-color:rgb(36,240,199);stop-opacity:0.4"></stop>
                        <stop offset="1" style="stop-color:rgb(36,240,199);stop-opacity:0"></stop>
                      </radialgradient>
                      <lineargradient id="_dia_ui_Linear3" x1="0" y1="0" x2="1" y2="0" gradientunits="userSpaceOnUse" gradienttransform="matrix(6.27121e-14,-1024.17,1024.17,6.27121e-14,560.196,1075.5)">
                        <stop offset="0" style="stop-color:rgb(13,190,155);stop-opacity:1"></stop>
                        <stop offset="0.56" style="stop-color:rgb(45,203,172);stop-opacity:1"></stop>
                        <stop offset="1" style="stop-color:rgb(170,255,238);stop-opacity:1"></stop>
                      </lineargradient>
                      <radialgradient id="_Radial4" cx="0" cy="0" r="1" gradientunits="userSpaceOnUse" gradienttransform="matrix(6.50783e-14,-1062.81,434.237,2.65894e-14,560.196,1120.39)">
                        <stop offset="0" style="stop-color:black;stop-opacity:1"></stop>
                        <stop offset="0.45" style="stop-color:rgb(5,12,17);stop-opacity:1"></stop>
                        <stop offset="1" style="stop-color:rgb(17,40,59);stop-opacity:1"></stop>
                      </radialgradient>
                    </defs>
                  </svg>
                  <style>
:root {
  --step-1-animation-timing: 10s;
}
#initial-message-group,#response-group{
font-family: Arial, sans-serif;
font-weight: 400;
}
#initial-message-group {
  opacity: 0;
  transform: translate(-40px, 80px);
  animation: initial-message-group var(--step-1-animation-timing) ease-in-out forwards infinite;
}
#user-blur {
  transform: translate(40px, 0px);
  animation: initial-message-group-blur var(--step-1-animation-timing) ease-in-out forwards infinite;
}
#initial-message-group>#read-status {
  opacity: 0;
  transform: translate(0px, 10px);
  animation: initial-message-group-read-status var(--step-1-animation-timing) ease-in-out forwards infinite;
}
@keyframes initial-message-group {
  0%,
  5% {
    opacity: 0;
    transform: translate(-40px, 100px);
  }
  10%,
  100% {
    opacity: 1;
    transform: translate(0px, 0px);
  }
}
@keyframes initial-message-group-blur {
  0%,
  5% {
    transform: translate(40px, 0px);
  }
  10%,
  100% {
    transform: translate(0px, 0px);
  }
}
@keyframes initial-message-group-read-status {
  0%,
  15% {
    opacity: 0;
    transform: translate(0px, 10px);
  }
  20%,
  100% {
    opacity: 1;
    transform: translate(0px, 0px);
  }
}
#typing-indicator {
  opacity: 0;
  transform: translate(0px, 10px);
  animation: typing-indicator var(--step-1-animation-timing) ease-in-out forwards infinite;
}
#typing-indicator circle:nth-child(1) {
  animation: typing 1.5s infinite ease-in-out;
  animation-delay: 0s;
}
#typing-indicator circle:nth-child(2) {
  animation: typing 1.5s infinite ease-in-out;
  animation-delay: 0.2s;
}
#typing-indicator circle:nth-child(3) {
  animation: typing 1.5s infinite ease-in-out;
  animation-delay: 0.4s;
}
@keyframes typing {
  0% {
    opacity: 0.2;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
@keyframes typing-indicator {
  0%,
  25% {
    opacity: 0;
    transform: translate(0px, 10px);
  }
  35%,
  50% {
    opacity: 1;
    transform: translate(0px, 0px);
  }
  51%,
  100% {
    opacity: 0;
    transform: translate(0px, 0px);
  }
}
#response-group {
  opacity: 0;
  transform: translate(40px, 100px);
  animation: response-group var(--step-1-animation-timing) ease-in-out forwards infinite;
}
#f18-blur {
  transform: translate(-40px, 0px);
  animation: response-group-blur var(--step-1-animation-timing) ease-in-out forwards infinite;
}
#response-group>#read-status1 {
  opacity: 0;
  transform: translate(0px, 10px);
  animation: response-group-read-status var(--step-1-animation-timing) ease-in-out forwards infinite;
}
@keyframes response-group {
  0%,
  50% {
    opacity: 0;
    transform: translate(40px, 40px);
  }
  55%,
  65% {
    opacity: 1;
    transform: translate(0px, -60px);
  }
  75%,
  100% {
    opacity: 1;
    transform: translate(0px, 0px);
  }
}
@keyframes response-group-blur {
  0%,
  50% {
    transform: translate(-60px, 0px);
  }
  55%,
  100% {
    transform: translate(-10px, 0px);
  }
}
@keyframes response-group-read-status {
  0%,
  65% {
    opacity: 0;
    transform: translate(0px, -40px);
  }
  75%,
  100% {
    opacity: 1;
    transform: translate(0px, 0px);
  }
}
</style>
                </div>
              </div>
              <div class="work-tile-text shrinkable">Chat with us about your project.<br>This Discovery session will help us understand exactly what you need.<br></div>
              <aside link="#chat" class="btn link inline on-light-bg">
                <div class="button-text">Start a Discovery session</div>
                <div class="icon right w-embed">&#xF135;</div>
              </aside>
            </div>
          </div>
          <div class="work-tile wide-work-tile transparent">
            <div class="w-layout-hflex work-tile-text">
              <div class="w-layout-hflex flex-header-tile">
                <h3 class="work-ile-title numbering-title">02 <span class="subtlise">Develop</span></h3>
              </div>
              <div class="animation-wrapper framed">
                <div class="how-it-works-video step2-animation-contiainer w-embed"><svg width="100%" height="100%" viewbox="0 0 1494 1494" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:square;stroke-miterlimit:1.5;">
                    <rect id="Artboard10" x="0" y="0" width="1493.86" height="1493.86" style="fill:none;"></rect>
                    <g id="vector-frame">
                      <path id="top" d="M59.509,47.111L1266.52,47.111" style="fill:none;stroke:rgb(13,121,242);stroke-width:11.11px;"></path>
                      <path id="right" d="M1266.52,47.111L1266.52,1192.22" style="fill:none;stroke:rgb(13,121,242);stroke-width:11.11px;"></path>
                      <path id="bottom" d="M59.509,1192.22L1266.52,1192.22" style="fill:none;stroke:rgb(13,121,242);stroke-width:11.11px;"></path>
                      <path id="left" d="M59.509,1192.22L59.509,47.111" style="fill:none;stroke:rgb(13,121,242);stroke-width:11.11px;"></path>
                    </g>
                    <g id="vector-anchors">
                      <g id="top-left">
                        <rect x="40.039" y="27.641" width="38.94" height="38.94" style="fill:white;"></rect>
                        <path d="M90.09,77.692L28.928,77.692L28.928,16.53L90.09,16.53L90.09,77.692ZM78.979,27.641L40.039,27.641L40.039,66.581L78.979,66.581L78.979,27.641Z" style="fill:rgb(13,121,242);"></path>
                      </g>
                      <g id="top-right">
                        <rect x="1247.05" y="27.641" width="38.94" height="38.94" style="fill:white;"></rect>
                        <path d="M1297.11,77.692L1235.94,77.692L1235.94,16.53L1297.11,16.53L1297.11,77.692ZM1285.99,27.641L1247.05,27.641L1247.05,66.581L1285.99,66.581L1285.99,27.641Z" style="fill:rgb(13,121,242);"></path>
                      </g>
                      <g id="bottom-left">
                        <rect x="40.039" y="1170.29" width="38.94" height="38.94" style="fill:white;"></rect>
                        <path d="M90.09,1220.34L28.928,1220.34L28.928,1159.17L90.09,1159.17L90.09,1220.34ZM78.979,1170.29L40.039,1170.29L40.039,1209.23L78.979,1209.23L78.979,1170.29Z" style="fill:rgb(13,121,242);"></path>
                      </g>
                      <g id="bottom-right">
                        <rect x="1247.05" y="1170.29" width="38.94" height="38.94" style="fill:white;"></rect>
                        <path d="M1297.11,1220.34L1235.94,1220.34L1235.94,1159.17L1297.11,1159.17L1297.11,1220.34ZM1285.99,1170.29L1247.05,1170.29L1247.05,1209.23L1285.99,1209.23L1285.99,1170.29Z" style="fill:rgb(13,121,242);"></path>
                      </g>
                    </g>
                    <g id="cursor-group">
                      <path d="M1283.67,1193.26C1279.38,1191.37 1274.39,1192.1 1270.82,1195.14C1267.25,1198.17 1265.74,1202.99 1266.92,1207.52C1272.27,1228.01 1279.98,1257.56 1286.09,1280.95C1287.89,1287.88 1293.49,1293.17 1300.51,1294.59C1307.53,1296.01 1314.74,1293.31 1319.11,1287.63C1321.33,1284.74 1323.47,1281.95 1325.39,1279.46C1330.77,1272.44 1337.58,1266.65 1345.36,1262.45C1348.13,1260.95 1351.22,1259.29 1354.44,1257.55C1360.74,1254.15 1364.56,1247.46 1364.27,1240.3C1363.99,1233.15 1359.65,1226.78 1353.1,1223.89C1330.98,1214.13 1303.04,1201.81 1283.67,1193.26Z" style="fill:rgb(13,121,242);"></path>
                      <path d="M1288.15,1183.1L1357.58,1213.72C1368.02,1218.32 1374.92,1228.46 1375.37,1239.86C1375.83,1251.26 1369.75,1261.91 1359.71,1267.33L1350.64,1272.23C1344.24,1275.68 1338.63,1280.45 1334.2,1286.23L1327.92,1294.4C1320.97,1303.45 1309.48,1307.75 1298.3,1305.48C1287.13,1303.22 1278.21,1294.79 1275.33,1283.75L1256.17,1210.33C1253.91,1201.67 1256.8,1192.48 1263.62,1186.68C1270.43,1180.88 1279.96,1179.48 1288.15,1183.1ZM1283.67,1193.26C1279.38,1191.37 1274.39,1192.1 1270.82,1195.14C1267.25,1198.17 1265.74,1202.99 1266.92,1207.52L1286.09,1280.95C1287.89,1287.88 1293.49,1293.17 1300.51,1294.59C1307.53,1296.01 1314.74,1293.31 1319.11,1287.63L1325.39,1279.46C1330.77,1272.44 1337.58,1266.65 1345.36,1262.45L1354.44,1257.55C1360.74,1254.15 1364.56,1247.46 1364.27,1240.3C1363.99,1233.15 1359.65,1226.78 1353.1,1223.89L1283.67,1193.26Z" style="fill:white;"></path>
                      <g id="f18-label-ui">
                        <path d="M1472.72,1403.67C1472.72,1444.4 1439.65,1477.47 1398.92,1477.47L1134.13,1477.47C1093.39,1477.47 1060.33,1444.4 1060.33,1403.67C1060.33,1362.94 1093.39,1329.87 1134.13,1329.87L1398.92,1329.87C1439.65,1329.87 1472.72,1362.94 1472.72,1403.67Z" style="fill:rgb(13,121,242);"></path>
                        <path d="M1483.83,1403.67C1483.83,1450.54 1445.79,1488.58 1398.92,1488.58L1134.13,1488.58C1087.26,1488.58 1049.21,1450.54 1049.21,1403.67C1049.21,1356.81 1087.26,1318.76 1134.13,1318.76L1398.92,1318.76C1445.79,1318.76 1483.83,1356.81 1483.83,1403.67ZM1472.72,1403.67C1472.72,1362.94 1439.65,1329.87 1398.92,1329.87L1134.13,1329.87C1093.39,1329.87 1060.33,1362.94 1060.33,1403.67C1060.33,1444.4 1093.39,1477.47 1134.13,1477.47L1398.92,1477.47C1439.65,1477.47 1472.72,1444.4 1472.72,1403.67Z" style="fill:white;"></path>
                        <g>
                          <circle cx="1139.72" cy="1403.67" r="43.506" style="fill:rgb(8,79,155);"></circle>
                          <path d="M1136.63,1360.27C1137.65,1360.2 1138.68,1360.17 1139.72,1360.17C1144.49,1360.17 1149.09,1360.94 1153.39,1362.36C1156.48,1363.38 1159.42,1364.75 1162.16,1366.4L1161.55,1375.8L1154.91,1405.26L1161.55,1431.07L1161.55,1441.3C1158.91,1442.84 1156.1,1444.1 1153.15,1445.06C1148.92,1446.43 1144.41,1447.18 1139.72,1447.18C1135.21,1447.18 1130.86,1446.49 1126.76,1445.21L1126.76,1395.49L1115.79,1403.05L1103.41,1383.83L1136.63,1360.27Z" style="fill:rgb(85,163,246);"></path>
                          <path d="M1153.39,1362.36C1156.48,1363.38 1159.42,1364.75 1162.16,1366.4C1174.78,1374.02 1183.23,1387.87 1183.23,1403.67C1183.23,1419.73 1174.51,1433.77 1161.55,1441.3C1158.91,1442.84 1156.1,1444.1 1153.15,1445.06C1150.59,1444.14 1148.26,1442.87 1146.17,1441.25C1143.46,1439.15 1141.27,1436.46 1139.59,1433.16C1137.91,1429.86 1137.07,1425.89 1137.07,1421.24C1137.07,1416.74 1138.14,1412.86 1140.28,1409.6C1142.43,1406.35 1145.25,1403.87 1148.74,1402.16L1148.74,1401.81C1145.84,1400.49 1143.37,1398.34 1141.32,1395.35C1139.28,1392.37 1138.26,1388.74 1138.26,1384.48C1138.26,1380.21 1139.05,1376.53 1140.63,1373.43C1142.21,1370.33 1144.31,1367.8 1146.91,1365.87C1148.86,1364.41 1151.02,1363.25 1153.39,1362.36Z" style="fill:rgb(240,255,249);"></path>
                        </g>
                        <g>
                          <path d="M1215.24,1428.65L1215.24,1377.78L1249.56,1377.78L1249.56,1383.78L1221.98,1383.78L1221.98,1399.54L1245.85,1399.54L1245.85,1405.54L1221.98,1405.54L1221.98,1428.65L1215.24,1428.65Z" style="fill:white;fill-rule:nonzero;"></path>
                          <rect x="1257.37" y="1377.78" width="6.246" height="50.874" style="fill:white;fill-rule:nonzero;"></rect>
                          <path d="M1297.35,1424.11C1295.04,1426.07 1292.81,1427.46 1290.67,1428.27C1288.53,1429.08 1286.23,1429.49 1283.78,1429.49C1279.73,1429.49 1276.62,1428.5 1274.44,1426.52C1272.27,1424.54 1271.18,1422.01 1271.18,1418.94C1271.18,1417.13 1271.59,1415.48 1272.41,1413.99C1273.24,1412.5 1274.31,1411.3 1275.64,1410.4C1276.97,1409.5 1278.47,1408.82 1280.14,1408.35C1281.36,1408.03 1283.21,1407.72 1285.69,1407.41C1290.73,1406.81 1294.44,1406.1 1296.83,1405.26C1296.85,1404.41 1296.86,1403.86 1296.86,1403.63C1296.86,1401.09 1296.27,1399.3 1295.09,1398.25C1293.5,1396.84 1291.13,1396.14 1287.98,1396.14C1285.04,1396.14 1282.87,1396.65 1281.47,1397.68C1280.07,1398.71 1279.04,1400.53 1278.37,1403.15L1272.26,1402.31C1272.81,1399.7 1273.73,1397.59 1275,1395.98C1276.27,1394.37 1278.11,1393.13 1280.52,1392.27C1282.92,1391.4 1285.71,1390.97 1288.88,1390.97C1292.03,1390.97 1294.58,1391.34 1296.55,1392.08C1298.52,1392.82 1299.96,1393.75 1300.89,1394.87C1301.81,1395.99 1302.46,1397.41 1302.83,1399.12C1303.04,1400.18 1303.14,1402.11 1303.14,1404.88L1303.14,1413.21C1303.14,1419.02 1303.28,1422.69 1303.54,1424.23C1303.81,1425.77 1304.34,1427.24 1305.12,1428.65L1298.6,1428.65C1297.95,1427.36 1297.53,1425.84 1297.35,1424.11ZM1296.83,1410.16C1294.56,1411.08 1291.16,1411.87 1286.63,1412.52C1284.06,1412.89 1282.24,1413.3 1281.18,1413.77C1280.11,1414.23 1279.29,1414.9 1278.71,1415.8C1278.13,1416.69 1277.85,1417.67 1277.85,1418.76C1277.85,1420.43 1278.48,1421.82 1279.74,1422.93C1281,1424.04 1282.84,1424.59 1285.27,1424.59C1287.68,1424.59 1289.82,1424.07 1291.69,1423.01C1293.57,1421.96 1294.94,1420.52 1295.82,1418.69C1296.49,1417.28 1296.83,1415.2 1296.83,1412.45L1296.83,1410.16Z" style="fill:white;fill-rule:nonzero;"></path>
                          <path d="M1326.46,1423.07L1327.37,1428.58C1325.61,1428.95 1324.04,1429.14 1322.65,1429.14C1320.38,1429.14 1318.62,1428.78 1317.37,1428.06C1316.12,1427.35 1315.24,1426.4 1314.74,1425.24C1314.23,1424.07 1313.97,1421.61 1313.97,1417.86L1313.97,1396.66L1309.39,1396.66L1309.39,1391.8L1313.97,1391.8L1313.97,1382.67L1320.18,1378.92L1320.18,1391.8L1326.46,1391.8L1326.46,1396.66L1320.18,1396.66L1320.18,1418.21C1320.18,1419.99 1320.29,1421.13 1320.51,1421.64C1320.73,1422.15 1321.09,1422.56 1321.59,1422.86C1322.09,1423.16 1322.8,1423.31 1323.72,1423.31C1324.42,1423.31 1325.33,1423.23 1326.46,1423.07Z" style="fill:white;fill-rule:nonzero;"></path>
                          <path d="M1374.11,1428.65L1367.87,1428.65L1367.87,1388.85C1366.36,1390.28 1364.39,1391.72 1361.95,1393.15C1359.51,1394.59 1357.32,1395.66 1355.37,1396.38L1355.37,1390.34C1358.87,1388.7 1361.92,1386.71 1364.53,1384.37C1367.15,1382.04 1369,1379.77 1370.09,1377.57L1374.11,1377.57L1374.11,1428.65Z" style="fill:white;fill-rule:nonzero;"></path>
                          <path d="M1399.72,1401.06C1397.13,1400.12 1395.21,1398.76 1393.96,1397C1392.71,1395.25 1392.09,1393.14 1392.09,1390.69C1392.09,1386.99 1393.42,1383.88 1396.08,1381.35C1398.74,1378.83 1402.28,1377.57 1406.7,1377.57C1411.14,1377.57 1414.71,1378.86 1417.42,1381.44C1420.13,1384.02 1421.48,1387.16 1421.48,1390.86C1421.48,1393.22 1420.86,1395.28 1419.62,1397.02C1418.39,1398.77 1416.51,1400.12 1413.98,1401.06C1417.11,1402.08 1419.49,1403.73 1421.12,1405.99C1422.75,1408.26 1423.56,1410.97 1423.56,1414.11C1423.56,1418.46 1422.02,1422.12 1418.95,1425.08C1415.87,1428.04 1411.82,1429.52 1406.8,1429.52C1401.78,1429.52 1397.73,1428.03 1394.65,1425.06C1391.58,1422.09 1390.04,1418.38 1390.04,1413.94C1390.04,1410.63 1390.88,1407.86 1392.56,1405.63C1394.23,1403.39 1396.62,1401.87 1399.72,1401.06ZM1398.47,1390.48C1398.47,1392.89 1399.25,1394.85 1400.8,1396.38C1402.35,1397.91 1404.36,1398.67 1406.84,1398.67C1409.24,1398.67 1411.21,1397.91 1412.75,1396.4C1414.29,1394.88 1415.06,1393.03 1415.06,1390.83C1415.06,1388.54 1414.27,1386.61 1412.68,1385.05C1411.1,1383.49 1409.13,1382.71 1406.77,1382.71C1404.38,1382.71 1402.4,1383.47 1400.83,1385C1399.26,1386.52 1398.47,1388.35 1398.47,1390.48ZM1396.46,1413.97C1396.46,1415.76 1396.88,1417.48 1397.73,1419.14C1398.57,1420.81 1399.83,1422.1 1401.49,1423.01C1403.16,1423.93 1404.95,1424.38 1406.87,1424.38C1409.86,1424.38 1412.32,1423.42 1414.26,1421.5C1416.2,1419.58 1417.18,1417.14 1417.18,1414.18C1417.18,1411.17 1416.18,1408.69 1414.17,1406.72C1412.17,1404.75 1409.67,1403.77 1406.66,1403.77C1403.72,1403.77 1401.29,1404.74 1399.36,1406.69C1397.43,1408.63 1396.46,1411.06 1396.46,1413.97Z" style="fill:white;fill-rule:nonzero;"></path>
                        </g>
                      </g>
                    </g>
                    <g id="text-code" transform="matrix(1.03924,0,0,1.03924,-7587.99,-246.924)">
                      <text x="7422.65px" y="473.302px" style="font-family:'CourierNewPSMT', 'Courier New', monospace;font-size:83.054px;fill:rgb(12,207,187);">mkdir new-app</text>
                      <text x="7422.65px" y="601.601px" style="font-family:'CourierNewPSMT', 'Courier New', monospace;font-size:83.054px;fill:rgb(12,207,187);">cd new-app</text>
                      <text x="7422.65px" y="729.901px" style="font-family:'CourierNewPSMT', 'Courier New', monospace;font-size:83.054px;fill:rgb(12,207,187);">git init</text>
                      <text x="7422.65px" y="986.499px" style="font-family:'CourierNewPSMT', 'Courier New', monospace;font-size:83.054px;fill:rgb(242,94,13);">&lt;code&gt;</text>
                      <text x="7422.65px" y="1114.8px" style="font-family:'CourierNewPSMT', 'Courier New', monospace;font-size:83.054px;fill:rgb(114,77,235);">echo(</text>
                      <text x="7671.85px" y="1114.8px" style="font-family:'CourierNewPSMT', 'Courier New', monospace;font-size:83.054px;fill:rgb(228,13,242);">&quot;Hello world!&quot;</text>
                      <text x="8369.62px" y="1114.8px" style="font-family:'CourierNewPSMT', 'Courier New', monospace;font-size:83.054px;fill:rgb(114,77,235);">);</text>
                      <g transform="matrix(83.0539,0,0,83.0539,7771.53,1243.1)">
                      </g>
                      <text x="7422.65px" y="1243.1px" style="font-family:'CourierNewPSMT', 'Courier New', monospace;font-size:83.054px;fill:rgb(242,94,13);">&lt;/code&gt;</text>
                    </g>
                  </svg>
                  <style>
  :root {
    --step-2-animation-timing: 10s;
  }
#vector-frame #top{
  transform-origin: left;
  animation: resizeTop var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes resizeTop {
  0% {
    transform: scale(.2, 1)translate(12%, 0%);
  }
  50%,
  100% {
    transform: scale(1, 1)translate(0%, 0%);
  }
}
#vector-frame #right{
  transform-origin: top;
  animation: resizeRight var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes resizeRight {
  0% {
    transform: scale(1, .2)translate(-63%, 10%);
  }
  50%,
  100% {
    transform: scale(1, 1)translate(0%, 0%);
  }
}
#vector-frame #bottom{
  transform-origin: left;
  animation: resizeBottom var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes resizeBottom {
  0% {
    transform: scale(.2, 1)translate(12%, -60%);
  }
  50%,
  100% {
    transform: scale(1, 1)translate(0%, 0%);
  }
}
#vector-frame #left{
  transform-origin: top;
  animation: resizeLeft var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes resizeLeft {
  0% {
    transform: scale(1, .2)translate(0%, 10%);
  }
  50%,
  100% {
    transform: scale(1, 1)translate(0%, 0%);
  }
}
#vector-anchors #top-right{
  animation: translateAnchor-TopRight var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes translateAnchor-TopRight {
  0% {
    transform: translate(-63%, 0%);
  }
  50%,
  100% {
    transform: translate(0%, 0%);
  }
}
#vector-anchors #bottom-right{
	transform: translate(-63%, -63%);
  animation: translateAnchor-BottomRight var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes translateAnchor-BottomRight {
  0% {
    transform: translate(-63%, -60%);
  }
  50%,
  100% {
    transform: translate(0%, 0%);
  }
}
#vector-anchors #bottom-left{
  animation: translateAnchor-BottomLeft var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes translateAnchor-BottomLeft {
  0% {
    transform: translate(0%, -60%);
  }
  50%,
  100% {
    transform: translate(0%, 0%);
  }
}
#cursor-group{
  animation: cursorGroup var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes cursorGroup{
  0% {
    transform: translate(-63%, -60%);
  }
  50%,
  100% {
    transform: translate(0%, 0%);
  }
}
#f18-label-ui{
  animation: f18-label-ui var(--step-2-animation-timing) ease-in-out infinite;
}
@keyframes f18-label-ui{
  0%, 25% {
    transform: translate(20%, -1%);
  }
  45%,
  100% {
    transform: translate(0%, 0%);
  }
}
@keyframes fadePanInText {
  0%,28% {
    opacity: 0;
    transform: translate(4%, 0px);
  }
  32%,100% {
    opacity: .75;
    transform: translate(0%, 0px);
  }
}
#text-code text {
  opacity: 0;
  font-weight: lighter;
  font-family: Fira Code, Courier New, Courier, monospace;
  animation: fadePanInText var(--step-2-animation-timing) ease-in-out infinite;
}
/* Adding animation delays for each child element */
#text-code text:nth-child(1) {
  animation-delay: 0s;
}
#text-code text:nth-child(2) {
  animation-delay: 0.1s;
}
#text-code text:nth-child(3) {
  animation-delay: .2s;
}
#text-code text:nth-child(4) {
  animation-delay: .3s;
}
#text-code text:nth-child(5) {
  animation-delay: .4s;
}
#text-code text:nth-child(6) {
  animation-delay: .5s;
}
#text-code text:nth-child(7) {
  animation-delay: .6s;
}
#text-code text:nth-child(8) {
  animation-delay: .7s;
}
#text-code text:nth-child(9) {
  animation-delay: .8s;
}
#text-code text:nth-child(10) {
  animation-delay: .9s;
}
#text-code text:nth-child(11) {
  animation-delay: 1s;
}
</style>
                </div>
              </div>
              <div class="work-tile-text">Choose a subscription and we’ll take care of everything else. You&#x27;ll get text updates as frequently as you or your team require. <br>You can also track tasks and progress online.</div>
              <a href="#pricing" class="btn link inline on-light-bg w-inline-block">
                <div class="button-text">Subscriptions</div>
                <div class="icon right w-embed">&#xF124;</div>
              </a>
            </div>
          </div>
          <div class="work-tile wide-work-tile transparent">
            <div class="w-layout-hflex work-tile-text">
              <div class="w-layout-hflex flex-header-tile">
                <h3 class="work-ile-title numbering-title">03 <span class="subtlise">Deliver</span></h3>
              </div>
              <div class="animation-wrapper framed">
                <div class="how-it-works-video step-3-animation-timing w-embed"><svg width="100%" height="100%" viewbox="0 0 1494 1494" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:square;stroke-miterlimit:1.5;">
                    <rect id="Artboard11" x="0" y="0" width="1493.86" height="1493.86" style="fill:none;"></rect>
                    <g id="confetti">
                      <rect x="121.288" y="30.205" width="52.28" height="52.28" style="fill:rgb(13,242,201);"></rect>
                      <rect x="16.973" y="175.275" width="52.28" height="52.28" style="fill:rgb(255,196,0);"></rect>
                      <rect x="218.359" y="108.625" width="52.28" height="52.28" style="fill:rgb(13,121,242);"></rect>
                      <rect x="312.742" y="187.044" width="52.28" height="52.28" style="fill:rgb(13,242,201);"></rect>
                      <rect x="422.204" y="108.625" width="52.28" height="52.28" style="fill:rgb(255,196,0);"></rect>
                      <rect x="539.041" y="160.905" width="52.28" height="52.28" style="fill:rgb(13,121,242);"></rect>
                      <rect x="655.439" y="82.485" width="52.28" height="52.28" style="fill:rgb(13,121,242);"></rect>
                      <rect x="765.339" y="160.905" width="52.28" height="52.28" style="fill:rgb(13,242,201);"></rect>
                      <rect x="886.395" y="56.345" width="52.28" height="52.28" style="fill:rgb(13,242,201);"></rect>
                      <rect x="1016.35" y="56.345" width="52.28" height="52.28" style="fill:rgb(255,196,0);"></rect>
                      <rect x="1092.62" y="160.905" width="52.28" height="52.28" style="fill:rgb(13,121,242);"></rect>
                      <rect x="1210.29" y="134.765" width="52.28" height="52.28" style="fill:rgb(13,242,201);"></rect>
                      <rect x="1424.61" y="134.765" width="52.28" height="52.28" style="fill:rgb(255,196,0);"></rect>
                      <rect x="1311.54" y="30.205" width="52.28" height="52.28" style="fill:rgb(13,121,242);"></rect>
                    </g>
                    <rect x="175.983" y="202.888" width="10.348" height="1229.92" style="fill:url(#_Linear1-confetti);"></rect>
                    <g id="previous-request">
                      <circle cx="181.157" cy="345.005" r="20.232" style="fill:rgb(13,242,201);"></circle>
                      <path d="M731.915,322.605L731.915,367.405C731.915,385.939 716.868,400.987 698.334,400.987L285.662,400.987C267.127,400.987 252.08,385.939 252.08,367.405L252.08,322.605C252.08,304.071 267.127,289.023 285.662,289.023L698.334,289.023C716.868,289.023 731.915,304.071 731.915,322.605Z" style="fill:none;stroke:rgb(13,242,201);stroke-width:5.56px;"></path>
                      <g transform="matrix(66.8532,0,0,66.8532,688.294,366.339)">
                      </g>
                      <text x="290.765px" y="366.339px" style="font-family:'Arial-BoldMT', 'Arial', sans-serif;font-weight:700;font-size:66.853px;fill:rgb(13,242,201);">New request</text>
                      <g transform="matrix(52.7965,0,0,52.7965,1188.48,363.757)">
                      </g>
                      <text x="854.019px" y="363.757px" style="font-family:'Arial-BoldMT', 'Arial', sans-serif;font-weight:700;font-size:52.796px;fill:rgb(13,242,201);fill-opacity:0.52;">48 Hours ago</text>
                      <g id="clock-icon" opacity="0.52">
                        <circle cx="813.989" cy="345.005" r="20.232" style="fill:none;stroke:rgb(13,242,201);stroke-width:3.32px;"></circle>
                        <path d="M813.989,332.269L813.989,345.005L805.523,345.005" style="fill:none;stroke:rgb(13,242,201);stroke-width:3.32px;"></path>
                      </g>
                    </g>
                    <g id="old-request" opacity="0.33">
                      <circle cx="180.624" cy="191.447" r="14.719" style="fill:rgb(13,242,201);"></circle>
                      <path d="M581.319,178.165L581.319,204.73C581.319,215.72 572.397,224.642 561.407,224.642L252.135,224.642C241.145,224.642 232.223,215.72 232.223,204.73L232.223,178.165C232.223,167.175 241.145,158.252 252.135,158.252L561.407,158.252C572.397,158.252 581.319,167.175 581.319,178.165Z" style="fill:rgb(13,242,201);"></path>
                    </g>
                    <g id="chat-bubble">
                      <path d="M1418.65,617.394L1418.65,912.275C1418.65,970.768 1371.16,1018.26 1312.67,1018.26L181.189,1018.26C122.695,1018.26 75.206,970.768 75.206,912.275L75.206,617.394C75.206,558.9 122.695,511.411 181.189,511.411L1312.67,511.411C1371.16,511.411 1418.65,558.9 1418.65,617.394Z" style="fill:rgb(16,37,54);"></path>
                      <clippath id="_chat-bubs_clip2">
                        <path d="M1418.65,617.394L1418.65,912.275C1418.65,970.768 1371.16,1018.26 1312.67,1018.26L181.189,1018.26C122.695,1018.26 75.206,970.768 75.206,912.275L75.206,617.394C75.206,558.9 122.695,511.411 181.189,511.411L1312.67,511.411C1371.16,511.411 1418.65,558.9 1418.65,617.394Z"></path>
                      </clippath>
                      <g clip-path="url(#_chat-bubs_clip2)">
                        <use xlink:href="#_Image3" x="75.206" y="646.896" width="1344px" height="372px"></use>
                      </g>
                      <path d="M1321.39,914.819L1296.66,948.13L1282.61,937.832" style="fill:none;stroke:rgb(13,228,117);stroke-width:5.19px;"></path>
                      <path d="M1338.37,914.819L1313.65,948.13L1310.24,945.633" style="fill:none;stroke:rgb(13,228,117);stroke-width:5.19px;"></path>
                      <g id="chat-bubble-heading">
                        <g>
                          <circle cx="188.067" cy="623.965" r="60.497" style="fill:rgb(8,79,155);"></circle>
                          <path d="M183.77,563.619C185.19,563.519 186.623,563.468 188.067,563.468C194.707,563.468 201.098,564.54 207.077,566.52C211.374,567.943 215.458,569.835 219.268,572.135L218.426,585.209L209.197,626.179L218.426,662.068L218.426,676.294C214.76,678.427 210.849,680.186 206.746,681.518C200.863,683.429 194.585,684.462 188.067,684.462C181.794,684.462 175.743,683.505 170.052,681.73L170.052,612.591L154.793,623.105L137.582,596.368L183.77,563.619Z" style="fill:rgb(85,163,246);"></path>
                          <path d="M207.077,566.52C211.374,567.943 215.458,569.835 219.268,572.135C236.819,582.73 248.564,601.987 248.564,623.965C248.564,646.293 236.442,665.811 218.426,676.294C214.76,678.427 210.849,680.186 206.746,681.518C203.179,680.234 199.942,678.465 197.034,676.212C193.275,673.301 190.227,669.553 187.89,664.97C185.552,660.387 184.383,654.861 184.383,648.39C184.383,642.136 185.873,636.744 188.852,632.215C191.831,627.685 195.75,624.235 200.609,621.862L200.609,621.377C196.575,619.544 193.138,616.551 190.296,612.399C187.454,608.248 186.033,603.206 186.033,597.275C186.033,591.344 187.133,586.222 189.333,581.908C191.533,577.595 194.444,574.09 198.065,571.394C200.774,569.377 203.778,567.753 207.077,566.52Z" style="fill:rgb(240,255,249);"></path>
                        </g>
                        <g transform="matrix(72.7468,0,0,72.7468,501.181,627.439)">
                        </g>
                        <text x="270.722px" y="627.439px" style="font-family:'Arial-BoldMT', 'Arial', sans-serif;font-weight:700;font-size:72.747px;fill:rgb(241,247,254);">Flat 18</text>
                        <g transform="matrix(36.5233,0,0,36.5233,379.482,672.35)">
                        </g>
                        <text x="273.907px" y="672.35px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:36.523px;fill:rgb(241,247,254);fill-opacity:0.5;">Online</text>
                        <circle cx="232.519" cy="665.473" r="16.045" style="fill:rgb(32,243,99);"></circle>
                        <path d="M232.519,641.095C245.974,641.095 256.898,652.018 256.898,665.473C256.898,678.928 245.974,689.852 232.519,689.852C219.064,689.852 208.14,678.928 208.14,665.473C208.14,652.018 219.064,641.095 232.519,641.095ZM232.519,649.428C223.663,649.428 216.474,656.618 216.474,665.473C216.474,674.329 223.663,681.518 232.519,681.518C241.375,681.518 248.564,674.329 248.564,665.473C248.564,656.618 241.375,649.428 232.519,649.428Z" style="fill:rgb(3,33,64);"></path>
                      </g>
                      <g transform="matrix(1.20589,0,0,1.20589,-10266.4,-215.906)">
                        <text x="8739.61px" y="835.101px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:53.303px;fill:rgb(241,247,254);">Latest requests pushed and ready</text>
                        <text x="8739.61px" y="899.137px" style="font-family:'ArialMT', 'Arial', sans-serif;font-size:53.303px;fill:rgb(241,247,254);">for feedback.</text>
                        <g transform="matrix(53.3032,0,0,53.3032,8846.21,963.174)">
                        </g>
                        <text x="8739.61px" y="963.174px" style="font-family:'AppleColorEmoji', 'Apple Color Emoji';font-size:53.303px;fill:rgb(241,247,254);">⚡️⚡️</text>
                      </g>
                    </g>
                    <g id="next-request">
                      <circle cx="180.466" cy="1225.5" r="17.228" style="fill:rgb(8,137,132);"></circle>
                      <path d="M662.235,1206.43L662.235,1244.58C662.235,1260.36 649.422,1273.17 633.64,1273.17L269.454,1273.17C253.672,1273.17 240.859,1260.36 240.859,1244.58L240.859,1206.43C240.859,1190.64 253.672,1177.83 269.454,1177.83L633.64,1177.83C649.422,1177.83 662.235,1190.64 662.235,1206.43Z" style="fill:none;stroke:rgb(8,137,132);stroke-width:5.56px;"></path>
                      <g transform="matrix(56.9272,0,0,56.9272,622.204,1243.67)">
                      </g>
                      <text x="277.36px" y="1243.67px" style="font-family:'Arial-BoldMT', 'Arial', sans-serif;font-weight:700;font-size:56.927px;fill:rgb(8,137,132);">Next request</text>
                      <g transform="matrix(44.9576,0,0,44.9576,868.127,1240.25)">
                      </g>
                      <text x="700.765px" y="1240.25px" style="font-family:'Arial-BoldMT', 'Arial', sans-serif;font-weight:700;font-size:44.958px;fill:rgb(8,137,132);fill-opacity:0.52;">Queued</text>
                    </g>
                    <defs>
                      <lineargradient id="_Linear1-confetti" x1="0" y1="0" x2="1" y2="0" gradientunits="userSpaceOnUse" gradienttransform="matrix(5.5529e-14,1229.92,-906.857,7.53106e-14,186.331,202.888)">
                        <stop offset="0" style="stop-color:rgb(13,242,201);stop-opacity:0"></stop>
                        <stop offset="0.23" style="stop-color:rgb(13,242,201);stop-opacity:0.78"></stop>
                        <stop offset="0.5" style="stop-color:rgb(13,242,201);stop-opacity:1"></stop>
                        <stop offset="0.75" style="stop-color:rgb(13,242,201);stop-opacity:0.75"></stop>
                        <stop offset="1" style="stop-color:rgb(13,242,201);stop-opacity:0"></stop>
                      </lineargradient>
                      <image id="_Image3" width="1344px" height="372px" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABUAAAAF0CAYAAADiopOTAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAgAElEQVR4nO29UXojuQ506eqvtjfLmFXNMmaBdx7u1P271ZKYJAhGADznqSUkAkGkymaiafvXDwAAAABANf7v//eX2gIAABjz//xf/1FbAAAf2DgCAAAAQC0YfgIAwBMYggLA/w+bRwAAAACoA8NPAACYgSEoAPwwAAUAAACAKjD8BACAFRiCAlzPX2oDAAAAAAAAAAAAAFnwf9EBAAAAwB9OfwIAQAROgQJcDRtJAAAAAPCG4ScAAOyAISjAtbCZBAAAAABfGH4CAMBOGIICXAkbSgAAAIAVGMwBAAAAvIdBM5jBxh0AAABgFoafAAAAAN9hCApGsHkHAAAAmIHhJwAAAMAzGIKCCWzgAQAAAJ7C8BMAAABgDoagYMBfagMAAAAAAAAAAAAAWXCKAQAAAOAJnP4EAAAAWINToCCGjTwAAADACIafAAAAADEYgoIQNvMAAAAA32D4CQAAALAHhqAggg09AAAAvIfBHwAAAADAfhgEH4cHGwAAAPg3DD8BAAAAAPJgCHoUHm4AAADgnzD8BAAAAADIhyHoMXjAAQAAgP8Dw08AAAAAgHMwBD3CX2oDAAAAAAAAAAAAAFlwygMAAAD+C6c/AQAAAADOwynQdHjQAQAAAIafAAAAAABKGIKmwsMOAADA7TD8BAAAAADQwxA0DR54AAAAVmFwCAB58PUFALJh0AIAORgOctlYAQAArMDwEwDy4OsLAJzCbkgBAE0wG4KyuQIAAJiF4ScA5MHXFwA4jdWQAgAaYTQEZYMFAAAwA8NPAMiDry8AoMJmSAEAzTAZgv6lNgAAAAAAAAAAAACQBf+XGQAA4Cmc/gSAPPj6AgBqLE5pAUBDDE6BstECAAB4AsNPAMiDry8A4IJ8SAEATREPQdlsAQAAjGD4CQB58PUFANxgCAoAOQiHoGy4AABAB4NFgEz49wUAANAXBtUAE7AxBgAADQw/ATLh3xcAAEB/GIICPITNMQAAnIfhJ0Am/PsCAAC4B4agAA9ggwwAAGdh+AmQCf++AAAA7oMhKMCAv9QGAAAAAAAAAAAAALLglAAAAJyD058AmfDvCwAA4F44BQrwBTbKAABwBoafAJnw7wsAAAAYggJ8gM0yAADkw/ATIBP+fQEAAMAfGIICvIENMwBAZRgswt3w+YfO8PkGgGwYlEFn+HzDP2BjBQBQFYafcDd8/qEzfL4B4BQMiaAzfL7hf7C5AgCoCMNPuBs+/9AZPt8AcBqGRNAZPt/w8/PDBgsAoB4MP+Fu+PxDZ/h8A4AKhkTQGT7f8POX2gAAAAAAAAAAAABAFvxfZgCASnD6E+6Gzz90hs83AKjhlBx0hs/35bDRAgCoAsNPuBs+/9AZPt8A4AJDIugMn++LYbMFAFABhp9wN3z+oTN8vgHADYZE0Bk+35fChgsA4BsMHsEbPp/wDT4fAAAAdWFQBxH4/LzAxhgA4BMMP8EbPp/wDT4fAAAA9WGIBRH4/PwNNscAAO9g+Ane8PmEb/D5AAAA6ANDLIjA5+f/hw0yAMArDD/BGz6f8A0+HwAAAP1giAUR+Pz8/Pz8pTYAAAAAAAAAAAAAkAWnBAAA/g6nP8EbPp/wDT4fAAAAfeEUH0S4/vPDRhkA4A8MP8EbPp/wDT4fAAAA/bl+iAUhrv78sFkGAPj5YfgJ7vD5hG/w+QAAALiHq4dYEObazw8bZgDwhsEk5MLnqzbcv7vh/gMAwDeuHfTAz88P97862+8fG0cA8IXhJ+TC56s23L+74f4DAMATGILdDfe/NlvvH5tHAPCE4SfkwuerNty/u+H+AwDADAzB7ob7X5tt948NJAD4wfATcuHzVRvu391w/wEAYAWGYHfD/a/Nlvv31w4RAAAAAAAAAAAAAEf4v+gA4AWnPyEXPl+14f7dDfcfAAAicArwbrj/tQnfPzaSAOADw0/Ihc9Xbbh/d8P9BwCAHTAEuxvuf21C94/NJAB4wPATcuHzVRvu391w/wEAYCcMwe6G+1+b5fvHhhIAvsNgEmLw+dFC/7XQ/95wfwEAvsOgqTfcXy30fxI2bgDwGYafEIPPjxb6r4X+94b7CwDwDIY0veH+aqH/E7B5A4D3MPyEGHx+tNB/LfS/N9xfAIA5GNL0hvurhf4/hA0cAPwbhp8Qg8+PFvqvhf73hvsLALAGQ5recH+10P8H/KU2AAAAAAAAAAAAAJAF/xcbAP4Jpz8hBp8fLfRfC/3vDfcXACAGp9R6w/3VQv8HsJEDgP8Dw0+IwedHC/3XQv97w/0FANgDQ5recH+10P8vsJkDgP/C8BNi8PnRQv+10P/ecH8BAPbCkKY33F8t9P8DbOgA3GEwCd/h85EL/Y1B/3Khv95wfwCgOwxavOH+5EJ/YxzvHxszAGcYfsJ3+HzkQn9j0L9c6K833B8AuAWGQN5wf3KhvzGO9o/NGYArDD/hO3w+cqG/MehfLvTXG+4PANwGQyBvuD+50N8Yx/rHBg3AEYaf8B0+H7nQ3xj0Lxf66w33BwBuhSGQN9yfXOhvjCP9++tEEQAAAACA5jD8BICb4WsgAFjDFykANzj9Cd/h85EL/Y1B/3Khv55wXwAA/gmn4TzhvuRCf2Ok948NG4ATDD/hO3w+cqG/MehfLvTXB+4FAMAcDIZ84F7kQn9jpPaPDRyACww/4Tt8PnKhvzHoXy70Vwv9BwDYC0MiLfQ/F/obI61/bOgAojC47A73Nxf6+53b+3P7+qPQPy30HwC6w6BHC/2PcXv/rls/GzOACAw/u8P9zYX+fuf2/ty+/ij07zz0HABu57qBigH0PMbt/btq/WzUAFZh+Nkd7m8u9Pc7t/fn9vVHoX9noM8AAN+5argihD7HuL1/16yfjRvACgw/u8P9zYX+fuf2/ty+/ij0Lx96DAAwxzUDFiH0OMbt/bti/X+pDQAAAADAFhjM5fLrhx4DAKzA18986C/AAP6RAMzC6c/ucH9zob/fub0/t69/FfqWC/0FANjLFafNhNDfNW7vW/v1s6EDmIHhZ3e4v7nQ3+/c3p/b1z8DvYpB/wAAatF+MJMM/XvO7b1qvX42gABPYfjZHe5vLvT3O7f35/b1j6A/MegfAEAvWg9pDkD/vnN7f9qunw0h9IfBZXW4fzFu71/39Xdf34jb1z+C/uRCfwEAvtN2kGIC/f1O9/50X9+I6fWzcYPeMPysDvcvxu39677+7usbcfv630FPcqG/AAAxbh/YZEN//033nnRf34ip9bORg74w/KwO9y/G7f3rvv7u6xtx+/r/QB9i0D8AAG9uH+5EoX//pXsfuq9vxOP1s/GDnjD8rA73L8bt/eu+/u7rG3H7+n9+6EEU+gcAUIvbBzxR6F//HnRf34hH6/8r2wUAAADAJm4fXP36oQcR6B8AQE34+h2D/vVff/f1bYEmQT84/Vkd7l+M2/vXff3d1/cN1g6r0D8AgF7cftotys3967727uv7xnDtbAihFww/q8P9i3F7/7qvv/v6vnHb2m9b7yz0Rwv9B5jn5qGEA/T/O7f1p/t6u6/vG1/XzgYG+sDwszrcvxi396/7+ruv7xs3rP2GNUagP1roP8B+bh5QOED/v3NDf7qvsfv6vvFx7WxoQA+Dy+pw/77TvT+sz5vq/kd0X9+I29cfhf4BAHhz8xBnB7f3r/v6q6/vuH82fqCF4Wd1uH/f6d4f1udNdf8juq/vHTeueSf0DwCgNtUHPmpu7F/3NVdf31H/bARBB8PP6nD/vtO9P6zPm+r+R3Rf3x9uWecq9EcL/QcYU304UR36/51b+tN9ndXXd8w/GxfQwPCzOty/73TvD+vzprr/Ed3X9/Nzxxoj0B8t9B9gnuoDiurQ/+/c0J/ua6y+viP+f58oAgAAUITqD/bV/Y9gfXdDf7TQf4B1/vz7qT6kqAr9/84N/fn1w/quh40MnIfTn9Xh/n2ne386r6/62qr7/0bntf389F9fFPqjhf4D7IdBhRb6/53u/em8vsprS/fOhgbOwvCzOty/73TvT+f1VV9bdf+vVF9Pdf8juq8vCv0BgG5UHqqc4Pb+VF9/df+vVF5Pqnc2aHAOhp/V4f59p3t/Oq+v+tqq+//5qb+G6v5HdF9fFPoDALdRecBygtv7U3391f3//NReQ5p3NmwwhsGlO9yf71TvT3X/I9zX5+5vRHX/I1jf3dAfLfQfYEzlIUQH6P93uveH9Wmx88fGBb7D8NMd7s93qvenuv8R7utz9zeiuv93dFzT3+m+vij0Rwv9B4hjNxC4DPr/ne796bg+9zVZ+WMjA59h+OkO9+c71ftT3f8I9/W5+xtR3f8fqq+juv8R3dcXhf4AQHWshheG3N6f6uuv7v8P7uuw8cfGDN7D8NMd7s93qvenuv8R7utz9zeiuv+fn/prqO5/RPf1RaE/ANANmwGGKbf3p/r6q/v/+fFfg4W/32oDAABwFe6DAXd/I/Cvpbr/Ed3XF4X+AEBX/nx9sxhiGHJ7f6qvv7r/n5//rsHZv4U/Nmrwbzj96Q735zvV+1Pd/zfc1+bu7xuVvf/84N+d7uuLQn8A4Dbkgwxzbu9P9fVX9l/Bu8wjGzb4Jww/3eH+fKd6f6r7/4b72tz9vVLN7yvu/t39jajuf0T39WVD/wDqUWGo4Uz3/lVfn7t/d3+v4PcDbIDg/8Dw0x3uz3eq96e6/2+4r83d389PDY/fcPfv7m9Edf8juq8vG/oH0I9qAw43uvev+vrc/bv7+/mp4fETad7ZEHWAwaU7t9+f6uvHfy74ywX/Wqr7H9F9fVHoDwBUp/IQ5QS396f6+vGfi50/NmbVYfjpzu33p/r68Z8L/vZT0fPfwb833dcXhf4AQHfsBhpm3N6f6uuv6L+SZ7lXNmqVYfjpzu33p/r68Z8L/vZQxecn3P27+xtR3f+I7uvLhv4B+CMfGBSne/+qr8/dv7u/P1Tx+cpx32x8qsLw053b70/19eM/F/zFqeDxG+7+3f2NqO5/RPf1ZUP/AOpRdcDhQvf+VV+fu393fz8/NTx+4pj336cKAQAA/Pg/eOMvF3f/7v5GVPc/ovv6sqF/AHX58++38pBDSff+VV+fu393fz8///Xo7O8bx/rLRqginP505/b7U339+M/D2dvPj7c/Z29PcPfv7m9Edf8juq8vG/oH0I+qgw4Xuvev+vrc/eMvj1TvbIiqwfDTndvvT/X14z8PZ28/P37+3Py8gr8Y7v5GVPc/ovv6sqF/cCOVBw4OdO9f9fW5+8dfDDd/aX7YoFSC4ac7t9+f6uvHfx7O3n5+PPw5ePgG/mK4+xtR3f+I7uvLhv4B/Bu3gUI1uvev+vrc/eMvhoO/FA9sWBxgsKnm9v5XX7+7f/x51x+Bv1zc/bv7G1Hd/4ju68uG/gH44zCIqEz3/lVfn7t//OXCX4G/Doafam7vf/X1u/vHn3f9EY7+HD3N4O7f3d+I6v5HdF9fNvQPoD7VBx5quvev+vrc/eNvP0c9sxFSwvBTze39r75+d//4864/wsWfi49P4C+Gu78R1f2P6L6+bOgf3EDFgYMT3ftXfX3u/vEXw8XfMR9sTFQw/FRze/+rr9/dP/68649w8Ofg4Rv4i+Hub0R1/yO6ry8b+gc34jJIqEr3/lVfn7t//MVw8HfEw+8TRQAAwAb1g6m6/gi1P3X9EfiL4e5vRHX/I7qvLxv6Bzfz5/PvMEioSPf+VV+fu3/8xXDw9+tEfTYqCjj9qeb2/ldfv7t/Z39qb+r631B7U9cfgb8Y7v5GVPc/ovv6sqF/AP/GddBRhe79q74+d//4i6H2l1afDctpGH6qub3/1dfv7t/Zn9qbuv4raj/U90btT10/SnX/I7qvbxf0CW5APSioQvc+ua8PfzHU/qi/CTYmJ2H4qeb2/ldfv7t/Z39qb+r6Pz96D9T3Ru1PXT9Kdf8juq9vN/QLbkQ9IKhG9365rw9/MdT+qL8IG5QnMLhUc3v/3dePvxhqf7fXH6H2p64/An8x3P2NqO5/RPf17YZ+AegHA9Xo3q/q63P3j78Yan/H67NRGcHwU83t/XdfP/5iqP3dXv8dak/q+iPwF8Pd34jq/kd0X18W9A3g36gHC1Xp3rfq63P3j78Yan/8ESQpDD/V3N5/9/XjL4ba3+31/6D2QX1v1P7U9aNU9z+i+/qi0B+4AfXAwJ3u/XFfH/5iqP1RfzNsTD7B8FPN7f13Xz/+Yqj93V7/50fvgfreqP2p60ep7n9E9/VFoT9wI+pBgTvd++O+PvzFUPuj/iZ+7xICAIBHqB8MqU/9m+uPUPtT149S3f+I7uuLQn/gZv58/tWDAle698d9ffiLofZH/U312ai8g9Ofam7vv/v68beO0pu6L9SnvjNqf+r6Uar7f6Xbema5ff0AGbgOdlzo1h/39eAvhtof9Rdhg/MKw081t/ffff34W+e0N3Uvutfvvj51ffX6Rqj9qes/pYrPVbqvLwr9gRtQDwLc6d4f9/W5+FP7oH7v+o/12Zj8HYafam7vv/v68bfOCW/q9Xev33196vrq9Y1Q+1PXn6Wa36d0Xdcu6A+AftDgTtf+uK/LzZ/aD/V71/+of8dGhcGmmu79d18f/qhPfepTfw21P3X9War5/USXdaxy+/oBMlAPHNzp0h/3dbj5c/Pzitof9TfTf4PD8FNN9/67rw9/1HeoSX3qu9Qfofanrr9KVd9/qO4/yu3rB1CgHiy4U70/7v5d/bn6+oPaH/WD9N7wMPxU073/7uvDH/VP1rm1fvf1qeur1zdC7U9df4S7v1m6rWc39AdAPyBwp1t/3Nej9kd96lvp992oMPxU073/7uvDH/VvWKOyfvf1qeur1zdC7U9df4S7vxHV/Ue5ff0AGagHEe5U74+7f7U/6lPfQv93pgsAgDeoH6y61+++PnX97utT11evb4Tan7r+CHd/I6r7j3L7+gEy+fPvSz2IcKV6f9z9q/1Rn/qZ9R/r99zocPpTTff+u6/P2Z/am7J+91Of3e9t9/Wp66vXN0LtT11/hLu/V6r5naX7+gDg37gO3nZRbX3uftX+qE99iX6/DRLDTzXd++++Pmd/am+n61Pvbn11/e6/Y1Z9//6g9qGuP8Ld34jq/qPcvn6AHagHDe5U74+7f7W/W+p3X2d1/f/Ra2PD8FNN9/67r8/Zn9obpy/9a1TXV9fvNgBX1xuh9qOuP8Ld3yeq+t7F7esHOIF6UOJO1f64+1b7u61+9/WW1a+x0WGwmU33/rqvT+2P+r3rd1+fun739d1Wb4Taj7r+CHd/f6jic5Xu6wOAf6MeuGRTZX3uPtX+bqt/23pP12/4V+AZfmbTvb/u61P7o37vmt3Xp6jffX3quur+fkLtS11/BP60dF8fAMyjHnxk474+/FHfqe4t9Yv/ESSGn9l076/7+tT+qN+rjqpu9YFj989hdX3qe9cf4e7vlWp+d3P7+gFOoB5AuFOtP+5+1f6612d93vr/wnejw/Azm+79dV+f2h/1e9RQ1q4+WOv+GayuT33v+iPwp6X7+gDg36gHMdm4rw9/d9dnfd76/+P3qUIAACYwGEJfSfX1de8f9WPgT0v39QHAZ/78+1cPYrJwXx/+7q7P+rz1/1XIC05/ZtO9v+7rU/u7uX7n4VD1tVXXV9evru9W/7b1utUfgT9vbl8/9MR1sHWK6ut396/2xx8HqqWvrl9W32+DwvAzm+79dV+f2t/N9bsNiKqvx12f36Fao+7NX9Mc6o/An5bu6wMA/aAlG/f14e/u+qfWV30dNvpeGyOGn9l076/7+tT+bq7fYchUfQ3u+gywqedc16X+CPxp6b4+ABijHjhl474+/HnVPVW/2wnatvpnNkoMNrOp3l93/2p/1EffuT76XvW6rUdVr/u/W/f6I/Dnze3rh56oBzhqqq/f3b/aX5eTjOr6DEK99P9F/gaF4Wc21fvr7l/tj/rou9VE37Nu98Fgl/5Rfw38eXP7+uFO1IMcNdXX7+5f7a/d4Otw/a4nbavrJ29YGH5mU72/7v7V/qiPvkOd2/Xd/an11fW7nkB1rT8Cf1q6rw8AxqgHS9m4rw9/2nqn66NfTD9vo8TwM5vq/XX3r/ZHffTVNW7Xd/en1lfXr65P/Rj48+b29UNP1IMcNdXX7+5f7c9uUFWsPvpF9H9nugCAkqgfXKoPNtCvre/uT62vrl9dn/ox8OfN7euH3vz5fKsHOSqqr9/dv9pfdn3Wh76Ffs5GhdOf2VTvr7t/tT9l/c610Uc/M7+7/ul61fXV9buvL4q7vxHV/WdDf+7EdbDlQvX+qP1Tv7a+uv5u/Wp+bfT3bxAYfmZTvb/u/tX+Og8gT9e7Xd/d3yn9LJ+3r9+lfnV96sfAnze3rx96oB7MqKm+fnf/an/VB13q+ugX09+7MWH4mU31/rr7V/vrPvysPqhw13f3d1rfXS9bX/X1pFqfTutTPwb+vLl9/XAH6oGOmurrd/ev9mc3sCpWP0u/qm87/T0bFQafUar3z92/2l/1B3b6h34lfXe9bP1T/16r9eW0vrp+9/VFcfc3orr/bOjPnagHK+5U74/aP/Vr66vr79Kv4tNWP75BYPgZpXr/3P2r/VV/yFX0r/qa0NfoV/mRcff1o1+zfvf1RXH3N6K6/2zoD7xDPVBxp3p/1P6pX1tfXT+q7+7PVj+2YWD4GaV6/9z9q/1Vf+DlJFkPfXd/2fru/rL1+d2+XvrUj4E/b25fP9yBejCjpvr63f2r/VUffKnr88eOzPXXNyoMP6NU75+7f7W/6g/p/E7Q+vru/rL13f1l67v7666vrt99fVHc/Y2o7j8b+nMn6sGKO9X7o/ZP/dr66vqc+DTR/53pAuBSqj/Yol9b391ftr67v2x9d3/d9dX1u68viru/EdX9Z0N/7ubP/VcPUlyp3h+1f+rn1md9ufnov1w4B6c/o1Tvn7t/pb/qD7foa7R36Lv7y9Z391dNL1u/mt9u+tT3rh+luv9s6E9Nqg7uTlG9P2r/1Ef/pP5tejb68xsAhp9RqvfP3T/DT/Rd9Xbrq/NP6d+yzix9d3/d9dX1u68viru/EdX9Z0N/7kA9OHKnen/U/qlfW19dnx9999KZ3Bgw/IxSvX/u/hl+oq/WyNRX55/Wv229u3VvX79KX12/+/qiuPsbUd1/NvQHfn70AxV3qvdH7Z/6tfVV9e0GgYd0s/SX9f67UWCwGaV6/9T+u9dHH32lvru/3XpV1nv7ALGKz6761PeuH6W6/2zoT03UgxF3qvdH7Z/66J/Qdx9gVunjst4vhp9hqvdP7b97ffT76bt7dveXpeu+bnd/2fru/rrrU9+7fpTq/rOhPz1RD4zcqd4ftX/qo5+pjz+RLn8FPkb1DZXaf/f66PfQd/epzs/WV+ef1nfXy9av5tetfvf1RXH3N6K6/2zoz118ut/qwZEL1fuj9k/93Poq/RFP6z/V/6TX1d9u/dnPw1CXAeg61TdZav/d66NfX9/dozo/W1+dn63v7i9b391fd33qe9ePUt1/NvSnJrsf/LtRvT9q/9TPre+iv1p/Vf+P3i3+dutv98cAFKAe1R+83fW7+2N9Wn13f9n67v6667vWpn6c6v6zoT/1+fs9rHKK8STV+6P271Jfde/cB2HV9fEX09/mjwHoGtU3UWr/3etn6uM9T7+7P9aH3s78bH13f931T9ehfg7V/WdDf2rw7oGXe/d/qN4ftf9T9TnhqdV3OYGJv5he2B8D0HkqfUN5h9p/9/o3DxCz9Xbrq/N36+zWdVmfOj9b391ftr67v+76LjWpv4fK3k9Af2ryet8qnm7MRH1yMYra/6n62YMldX13fffB623+nupt98cAdI7qGye1/+71bx9+untU57vrdfPjOljerXNKd5e+u7/u+m51qT9HFZ9Z3L5++C/Rz0HFIeFT1MPEKGr/ivrqAX92/dv18RfT3e6PAeh/qb6hUvvvXt9d391fdX23weBuvW5+bhlQuvQpS9/dX3f9UzWon0Nl7yegPzVR/3VoZ9Q/Rh5F7f9EfX4EXqvvfhK1u7/oIPRV5xV+BH6CSt8c3qH2372+u767v1OaO/UZfGl0q+dn66vzs/Xd/XXXP12H+jGq+Mzi9vXfCic/P6M+ORlF7f9EfU56avXxF9Pf5W/XIPST7h8+6t8+AK2+gVL7717fXd/d326dLP3q+dn6t+ffrrdb391fd32XmtSfo4LHTG5fP+xBPYA6hXqYGEXt/1T97JOA6vru+u4nUbv4i/4O0Kd6j/VvHoBW30yp/Xev767v7m+XRqZ+9fxsffJzUfvrvj70vfWpH6eCx0xuXz/Mof4R6xNUX6Paf3Z9fgReq+8+eO3qb/eJz7C/mwegUJfqD37dBwPd/anzs/XJz0Xtr/v6lPqVvZ/Qp34Md3/Z3L5+iHHD6U/1yckoav/Z9VnfHv0df0zH2V/2r2BQ+bNZ360D0OqbKLV/Zf3qD4DOwwH3U5/q3qnzs/XJ34vajzo/W9/d326drvrUj+HuL5vb1w/PUJ8szKb6+tT+FSc/Wd8znXfMarv463JCVp3/VJ/fAfo3Kn1DeIfaP8NPnb7bgMZd7/b8XTrk56L21319Cv2Knk/qO9dXr32Eu79sbl8/rNH99Gf19d10MjKrxtP6zic/swZjLv5ODf5W9V3zR0z/jtHbBqDVN05q/50fStz1u5/O3KFxe/5uPfJzUPu64WudQr+q71P6LvXV6xzh7m9Edf9wB9mfU/UAMro+pf/uw9DXGu849YeXnIehnzSjuo7+sgfkan+n1nfd7wCtvuFS+6/+YHW7Pv7uzt+tR/4e1D7U+dn67v52aXTWd66vXvsId3/Z3L5+WEP917WzyT5Jlkn3HxP/VOMdGeuu8GPwmf1x8MePwO/JH+le8TtAq2+C1P6rP0Ddru/+45uO/qrnZ+mST34m3f3t1umq71Jfvc4R7v5GVPcPPeHk51++sxIAACAASURBVHc4+Zlbn5OfzzUiOiNtlT9Oeubmv+q0/R2g1TdYav/VH6Ru13d/YHfxRz75yvzdeur8bH13f6c0O+k71FevcYS7vxHV/QPsIHvAkY2L/xuGoaO6mbUrDENfdf4OvxP0jL5r/lO9j/mVB6DVN1tq/9UfqG7X3+Evc40O/sgnn3xdfrY+/mrrO9RXr3GEu78R1f0DzKD+Ee4oDv7VHpT1VT+Cv7NGVF/xI+In/fEj8HvyP+ld9ztAAXbi/mCpzs/WJ5988uvmZ2p3/9rVXV9dX72+Ee7+RlT3DxDB5fTkKg7+bz35+Vo7q777yc/u/k6t79ggsWp+1QFo9U2W2n/1hwznB/RsfXV+pjb5tfKr+SXfK3+XTvev2d311fXV6xvh7m9Edf8AT1CfXIyi9k/93Pru+t397VpflROa9vkVB6CVvqG8Q+2/+sOE8wAvW1+dv1vvVH4Vn+ST3zE/Q7P71+ru+sra6j3YCHd/I6r7B1jB4fRkBLV/6ufWdtfv7m/X+uwHi1Xyqw1Aq2+s1P6rP1gw/NTlO3jghGKN/Kq+yd+Tn6Xb/Wt0d/1Tdbrvs6K4+8vm9vV3wX3AmP05y15/1P/OP1LjWP/2v9aeqd/dX8b6ogP86/JdBqDVNyRq/9UfKNz1u/vrtj51P27Jr+KTfM98Fw+Z+u7+1PrK2up92wh3fyOq+wdvXD9fpwazWevP/uvTXepn9N/9x8Cz9bv727G+cictXfMdBqCu38SeovZf/SHBXb+7v4z1cfKwZ767P/K983fpdP+a3F3/VJ3ue7Mo7v6yuX39VeHkZy7uJy/V9Tn5maff3d+O9ZU7aemarx6AVt+AqP1Xf4Bw1+/ur8tAg8GdV341v+Tvzc/Q7P61uLv+iRrd92NR3P1lc/v6uxB9AK6O+/rV/rLrn1qf+zAwW7+7v13rsz9p6ZqvHIBW34yo/Vd/mHDX7+5vx/rUHhh8avPd/ZFPvlLf3Z9a/0SN7vu0KO7+srl9/dVQ/6VuNe7rV/tz/xFqhxru+t39ZeS/o+xg8lS++gQoQAbdH1zJ1+rfnu/uLzO/snfy8/Xd/aHfu/4Id3/Z3L7+6qhPF6pxX7/aX4fTn+4nH7P1u/sjf09+9FcI/Ec1AK2+CVH7r/4Qkanv/ACr9tY9393f7nz3etX6uZpfxSf5Gn13f+j3rj/C3V82t6+/CurThWrc16/2535yEP3c/Gx98s/kHzshqhiAOn3BXkHtv/rDQucB4269W/JVPt39Vcl395eRX9Ez+We0K38PRb9+/RHu/rK5ff1VUZ8uVOO+frW/6qc/O+lHTwauajzVr3zysXt+eLD5NP/0ALT6xkPtv/qDQ/eHVrWHavnu9ar183Rdd3+786v6Jn+vjvv3SXd/an3qx3D3p4b+/Bf3v1auRv3X0keo/bnX766/6w/z7Ppr568o/ZF/Pj86IP9X/qkBqPs3KrW/6g8U7vrq/Gz9avnu9ar0030QWKWPkfwKHsnXaLt/30C/d/0R7v6yuX392WT11+3k4yqr/Tm1frU/1/q36K/0f8ePsTv7Iz8/v9WPwLtvMtT+qj8EuOur8zM0q+a7Dwbd+4o/j/wqPsnP1en+va+7PvVjuPtTc2t/OPn5HfXJxhFqf+71u+tz8pN8Zf72k56fLswegLp/o1L7q/4A4a6vzt+l0z0ff3vz3etV66eqJvn78l08ZOq7+1PrUz+Guz819Oe/RB9gu+PeH7W/7vXd9Xf52zEMHelGtCsMA8nfcNLzU37mANR9M6D2V/1hwl1fne/gwT0ff7F8/Gnz3f2RX8NDpr67P7U+9WO4+1NzW3/UfwncHff+qP11r++un/Uj7LvW6OCP/Nz8Vj8CDzCL+qGP/Nx8/MXy8aer7d578r3zs/Xd/an1qR/D3Z+a2/ujPr3njnt/1P6613fX3+Ev6+Tnq/aqfpWTj93zn/wIfKq/rAGo+yZA7a/6Q0SmvvoBsHK+u3f8eV9/ut4pf90+N+T3ys/Wd/en1net7VB/hLs/B27pkfr0XgVee+TWH7U/ZX3305nZ+ur8bH3yz+Tv/Kvuqf4yBqBuX9BfUfur/rDA8NMnv4rf1ToMuJ7l4+98TfeeZ+ZX9u6Qn63v7k+t71rbof4Id38O3Noj9em9Crj3SO1PWd/9dGa2vjo/W98pv/LJzVH+p+9/M3+c64i/3QNQ92/8an/VHxwYfmrzK3h0vn42X9Vvl8FyNP+0v66fh9P5VXy65mfrq/O766tqOdYf4e7PkS49y/5r1h1Rn7AcofZ3sr77X2PP1lfnj1D7y8yvMszcmT8zQD7ib9cA1O2L+CvVN+S365NfK9/t+tl890GT+yDLyV+Xz4pjfmXvDvnZ+u7+1PouNZ3qj3D350T3Xq2e9LkZ9QnLEWp/J+tnf37d9dX52fqO+VV+jD2S796frT8C7/5Nvvom/XZ98mvUnMm79YRfdf1ovvtg+Wm+e5935Vfx6Zqfra/O765/qoZz/RHu/pyp2jtOfq6jPmE5Qu3vRH33k4vZ+ur8EWp/nPyM5Uf+J8ZRf9EBqNsX71eqb8Bv1yffq85qPvq1rz9dz3Ho7/5vbEd+BY/O+dn66vzu+qdqONcf4e7PmS69izzg3o5779T+TtTPruGur87P1q+SX2GYuSP/0/c9+e8EjQxA3b+ZV9+M365PvkeNSD76vfWj+ej751fw6Jyfra/OR793/RHu/typ1j/+0nsM9QnLEWp/2fXd/1J5tr46P1u/Qn4Fj5H8jF8xkOIv46/AA6gfyrrn3+4Pfa1+JLf62t3/7UXz3f2552frq/PR711/hLs/d6r3T31CsDru/VP7cz9dWF1fnZ+tXyG/ysnN1fwy/lYHoO7fxKtvkivrqx/QnIc/0Xz33tymf9v1q/nV7qubvjrf3Z97fqb2Dm/O6+uu715/hLs/d6r1T31CsDru/VP7O30CtJK++uTfO41O61OcjHTyF80v529lAOr2BfuV6hviyvrqh7nT+S4DJJX+03z0e+uv5FRfc7WvVbO57v6c8zO1GXjW1nevP8Ld325uW++I7H64nYjcTfSEWTZqf9n1K+tHtXd467y+nfk7/1p6Rn72yc+VGif9/efnZ34A6r4ZqL45rqyvfrA7ke8+YHUb4N6qX32wnZXner9c9Hfln77vs/lV+piVv1Mr43u60/q667vXH+Huz5XqfcsajFXvywj1CcsRan+VT2ju1lf/NfMnmpXXp/5r7+7+ntbOWN+RYejvv78wRu3PfZPd3d9t+W7Xz+bfol99gOnwPw1c7qWrfma+4//0cam9Iz9D03046b4XUeu71x/h7m8Xt6xzlez+uJ2M3EX0lFQ2an/Z9Svpf/o3lvFHZp7SaX0Z+TtPRqr8fSN7fUd/DP73pLgCtT/3DXd3fxXzXQdX1YdBan11fXd9Fx/V9Xflu36dOF3HNd/FQ6a2+/5Ere9ef4S7Pze69YuToHOoT1iOUPurdEIzS199snFGu+L61Ccr3f09rV325OefuPtfgVd/c3DfXHf3VyW/+sAF/b36t13/JMftHrnp78iP1HD31yHfxUOmtvueRK3vXn+Eu78o3de3m+x+uZ2QjLJ6WuoUan/Z9SvoRzUy19hhfafyV4d57v6i+S7+fjkPQNUbEfeNdnd/FfKrD13Qr12/ur66vrv+jnyGouQr9d39qfXd649w9xel+/rcUJ9AzMZ9fWp/FU5oZuqr/5L2rH619al+BPxpDXd/0XwHf/+LOw9AQYf6occ9H/279dX1Vdo79NX13fWj+fgjX6nv7k+t715/hLu/KN3X50709JM77utT+8uu766vzs/WvyH/7bBto340P+Ivmu/g739x1wGoehPivsnO9Kf21vnEklq/+nBjVn/39er6q9e73zd1fXf9aC7+PGvvyN+lkaXtvpdS67vXH+HuL0r39bmhPoGYjfv61P7cT2hm66vzZ7Rn9W/Ij/T/6B8AStCP5h/XdxyAqr8huG+oOz/sZA+MTtdzuX61T10GbNn66vqnNHfqq+sr9W8fKLr7c87fpZGl7b5/Uuur67nVj1LdP/yT7PupPnEZXV+2/5G/0/XdTjBm66vzM7VvyI/U2Olvt7cn+tH84/puA1D1ZsZ9c935wafDg7Obvvp61yGUerCbNbh2vx/q+qf0T36dyhiO78zv6M8pP1vTcX2d9N3qPsXdX5Tu63Mna9Dmcl9X13fK/+pfeN5dd3c9d311/jetV6LD8o75M/3P9PdkmJit/w2LYajLALT6JtZdn/zcfDd99fUu/cgaUKrrz1znfq/U9ZX62cNBxdfNTv4c87O0dmm674XU+qfruNaPUt0/fOf0oO000fV1PQn6WtfthGa2vjr/m9as5o35M/3P8Lfz5Oeq/jesfgzeYQBafUPrrn9jvvsJoix99WBOPWy6vf5unSx9df1T+ie+Dp0ewM7md/bnkJ+t7by+DvrqerO4+4vSfX3V4CToe075P30S1P2EZra+Ov+dxivR4Xjn/Cf95+Tn5/yj+uoBaPXNq7v+Lfkqn+qB49Pr1AMhdf3s69X1n1yjvgfq+if03Qd2+KvzPU2l7bw+9OvXj1LdP8yRfb85Cfqd6LAoWu82fXW+g4fK+U9yT/lbHSZm6z/NT9dXDkCrbzTd9W/IV3p0GAa4D4Wo713f3V92/RP6Dh6U+fjLzc/WV+ej37t+lOr+4SzZJwCzUft3P0FZXd8tf1aD/O+5J/xFamTrP8k/pq8+AQrvUT90uOff7o/6efXVg+cTa3f3V1lfXT+qH83HX25+tr46H/3e9aNU9w9anjzMO6P2n13/dn3ye+ef8DcaJir1n+Qf01cNQKtvMjP11Q8g7vlKf2pv1N9b/+n1u6/brZt1X9z9ndBXD8Qd9KO52frRfHd/Sn11Pvq960ep7h/Ooj5BGUXt3/0EZXV98uvmr5yeRH+u/lZ9xQC0+oaS4acu/7S/rMHXbn33+urPxUr9mRxF/Z013f2d1N81MOs6VHT29jTf3d9JPTc/2Xrd9Klfm2r9qXZi8pXsfnf7nZyn63fTV59AfOX0HwhSr7/T7//spJ/9u1EfDUNPD0CrbzYZfuryHR+M1de711d/JtQDb9d+Zw0H3T8PKz4yrlX/D4ud+er/uZCR7+7vhO4uX67r667vXj9Kdf9V6dL3rEHhqf6s/oXnKvWr6qtPMH7SmdXb9RfET6//ZP7KMBn9f+ft+qzN6P/6+dk3AFV/U3TfrJKfm3/an/p69/rq+71zUJQ5QNlRP/NeuPvL0N89/FL3f3eu03A0mu/uL1PHfTDpvqdT61O/NtX7w0nQ71Q/iamuX1X/Vff0CcZPOq9knQRVr/9k/qcezeTcpr/z5OeK/j/iOwag6m/k7htZ8nPzHR+YVz2dHOxl1Fff6+r926WT5X9XffXnZOY69QBcoT+T7zzgjea7+8vQc/OTrddN371+lOr+u9DlPnAS1Lt+NX31CcaR3qzubL56/SfyI8Pj2/U/DiMf1o/q/yMeHYCqvwm6b1bJz80/5U/to3p9d39Pr1m5dlf9nT3OGOq5fwZ2XnNa+4T+iRoua4jkun9PzM7P1nf3p9anfm269YeToN+pfhJTXb+a/que6gToJ71Xdp8EVa//RH6kBvrvr9vt4Yn+r8gAVP2N3H0jS35uvsPDvnqN7vWr+8O/Nj7ixFDUeaDmMLCrrh/Nd/eXnZ+t7+5PrU/92lTrz+4Tat1Q96d7fXd9dX62/uzAs1r/Vga6MzXQj3nYof+/uOKvwIM/6ocW9UMn9XPj0dzK/tXxEfiLgb63fjTf3V92fra+uz+1PvVrU70/Tx42b0bdn+713fXV+dn6swPPav1Tr+8G/bfDyE31n+j/L746AFV/E3ffxFbOd6+tHE6p66sf7nf7e3r9rutc/avjI5z9OQzMo7nqr2k360fz3f1l52fru/tT61O/NtX6oz5h6I66P93ru+ur87P1OQHqq79yOnKnvnv9f8VXBqDqb3juG9bK+adru1y/+7rduqv3pcqALppT0b86PsLV366Boru/1Vz1fXfXP319t/zdeu77ue767vVHuPvrRvV+q38n5ojs3/mort9dX50/YjSQiubvXj8nQPdp79Tf9aPuWfUf1Z4dgKq/+blvNivnn6id7S/zYVZRX/0wf3rIdXpgfdq/Oj7Cxd/uf5dPr1X7y9BXfybc9aN5lb/n78jP0nXf66n1qV+b6v2p+qPvp/q+2p9d/lzr36Kvzn+qO6v3NH/VPydAn+uvDNN36Y+Gjer6j/w9HYCqv1l330y753fz5z6odF/PyUFSxgDJyb86PsLN3+n/aaH2N5ujvt/u+tE67t8Ls/N36bjvybrru9cf4e6vO9X7z0lQbf3u+ur8p7pZJyxX/Z/yt5rvpD87fN6hHz3VmV1/yt+TAaj6G133jbZ7vsJf1hAh4+F457Xqh3d1fPa63fVP+VfHR7j4cx2Qz1wXuReKAe5t+qev75a/W899v6bWp35tuvWHk6DfcT2Jqa5/i746f6Q3q7PrR96fDu+y/c3mO+hHhudR/dGwUV1/yt9oAKr+Zt19M+2ef8pf1sDr6XXu9dX3+YT/rKHzk+tP+FfHRzj4yxwouvvbpa/+HNyuT35uPvq9649w93cb1e8HJ0G19bvrq/NHetn5s/5P+5vNd9CPeNip/2TYqKw/9PdtAKr+xtZ9o+2ef8Jf5WGBw1BMXV8dH6H25x4f4eBP+W9Y7W+Hvrr+7frk5+aj37v+CHd/4EX0BFc2an/Z9W/XJ/97/u3+dgxEd9bf7eFE/cf+Vv4KPOg3/dUfCtX67vWr+1OuX+3NPT5C7a/7+kZUr3+7Pvm5+ej3rj/C3R948+RhWonaX3b92/XJ/55/uz/3+q/XONZ/7O/TAFS9yXDeZKu9VX9AzBxuqetXH164x1evV/t2j0dz1f6d/00/vaZz/Zv1yc/NR793/RHu/sAL9QnLEWp/7icoq+uT/z3/dn+d66+c7kz1924Aqv6G4LyhVnur/jCYNeBS11cNcFz8qeOKmtXjkbwKQ1FlXfXAUV3/Zn11PfLRP4m6fpTq/rvh/js5o0R/Z6J7fXf9238n6O787BOAr7j5u+0EaKT+k9yj/l4HoOpvDs6bZbW36g+GFR7cTw9c3QfC7vHT9arHZ6/bfW3F9SmG893r36TvVrdLPvq9649w9wdrnLqvqh99f7q+LH/Z9d31d32+ovpV80+d0HT1p9avVH9lGH/U3+8HF+3AXZ/8u/XdBm3Z11ePz1yn9uoej1xfof/KehnDyUr1b9Q/XYd89BWo60ep7r87nAStXd9dn5Oga/mjgdOIp/mu/tT6lerPDrd31J/y9/tNwm7c9cmvp79zCHBSK/vhWT1sUsddfLjHZ65TDNGc1pf59fGEP+f6N+ir63XLR793/RHu/mAPp+4zJ0Fz6rvr7/p8VT3JuZp/+nd0uvlT61eoHxm+H/WX/Vfg3TeL5NfQVw0Ed+m497l63MGDe3znNSt5VdbXeeCoro9+XJ989JWo60ep7v82OAlau767PidB5/JHA6cRs/lu/tT6FepHPB71lzkAdd8Iku+vn/mw7zAsUfeY+N3xXRpZ2g7rq/5vuHv92/XJR1+Jun6U6v67ET3h5I56fdn13fW7+1OfEIzqR/O767vXfzIQ/cZRf9knQCGH6g9t1euzPs/axPlsquMjqI8++Xn56PeuH6W6/+48eaCujHp92fXd9bv7y86/3Z9a371+KX9ZA1D3TWDlfOcHuCe5yvru8RFqf6uDT2ffxPk3OaJyf7rXv12ffPSVqOtHqe6/G+oTktmo1+d+AjJbv7s/9QnBqH40v7u+e/2Iv9Hp0ZUft/8azxiAum/4Kue7Paztvj6rvnqIsnrf3NfH0PPeuKquw8Dz6TUrebd+jeb6NcjPBf3a9d2p1p/uv5NzRPbvjIzi/js3s/W7+zudPzvQ6uav2glHdf2Iv+iw88k1/4jvHoC6b+Yq5zsMPzP9Z9RXD1PUD8+nB5/qfhLPjc9et6OmOr5yrVN/onnu9W/XJx99Ber6Uar7V3Gqb64/+r5r/Vnre+pvtb67fnd/2flPB4pd/VU/gamuP+Pv27/B0TD0k7bkBKj7Zq97fnf9KoPBp/Hs6zPj72Kz1xPvEz9dz23g6daf2euq1b9dn/w9+ej3ru9O9f5wEjRG9vrcT0Jm63f3l5U/GmhF9aP52f6iA78R2QNHdf0Zf08GmFHN9BOg7hu57vmO+juHDCe1ug8ndsc/Xe/mk/je+Mx1aq/Kr28O/cm+Xl3/9uvJR/8k6vpRqvt34VQfOQm6xlN/nARdQ+0vK3/Xj5RX9Vf9BKa6/hN/GSc/n9bfegLUfbPXPd9FXzXQ26Wjjo9Q+3sXfzoMJd4jvusa93gkr0J/RlSvf7s++egrUdd3p1t/OAkaI3t97ichs/W7+9udPxpoRfWj+dn+ogO/EdkDR3X9J/6eruHJMHRW+x/xyADUfSPXPd9BP+LhRH33+Ai1v0/xp8NPV//E98QdPCj/jXboz4jq9W/XJx99Jer67lTrT/QEUnXc15/tz12/uz/1CcCofjRfrU/9vfHVaz4h+R2g8E+qPzRVr+8eH/EtX+m9w+DT2RtxfXyE2l/19WXXv12ffPSVqOu7U70/Tx5WO+O+/mx/7vrv/n3t9Ljbn1t+tH+n/alPQM56ODFQVNbfPRBNXd/qANR9E6fMr/4AlVn/RG31gCGrP25DvafvucTd+ndLPPpv1uFrQqa2Oj6iev3b9clHX4m6vjvV+uN+AjIb9/W7n4A8pf93nZ0e3U9I7spf7Z/6hOIp/az+uK9fHf/G9DB1ZQDqvmFj+JmnP1v/6fW76qqHCbv74zq0e42p+/op7tq/7nHngerMNSt57vERbl8DuT4G+bmgX7s+zNH9fmX/zsgo7r/TUqm/Y+jp9jsvT+Y/6d9pf7MDsUz9jP7sHgierq84IfqJr8POd/HZAaj7ZozhZ55+xlBh55rVg4Wd/VkZ6KnijkPGSv27IZ75ud/5NSJD0z0+QlU/417dqE8++grU9d2p3h+3H/3exdP7olp/tr/K+u+0d/wI/Tue/OGWavlP+pftb+UE5in9Hf3JOIF6sr46/u3aT3kf7+XTAaj7Zs09v7u+cvC5Q08dH13rOGT8FnPz5Nq/zvHo/xCIDlN3eFi53j0+4lT96oNFV33y9+Sj37s+xOh+/zgJWlefk6B1T4KOBmIO+pH+RAeCo7rZ9dXxb9d+uv7jMPTJANR9I+ae311/50Ai4+FQPXCIeHUcMH6Lu/mq4rNTfHYAHc1f+fq38+uM8714Eh9xuj7X772efPRPoq7vTrf+cBJUQ7a/yvpPByOzGu9wPMkZzX/Svyx/u37kO1M/0p9TP/KeVV8df3fNp+uHw9DRANR9s+ae311/1yAha53q4UKkP0+HeQ5xR2+V+tc1njH4zPpasOsa9/iI7v5u1ycffSXq+rCX7veTk6B19TkJWu8k6Ggg5qS/0p/oQPCV3et3jz+95vW6tzrfBqDuGzH3/O76DsMCZe3M/jwd3qnX4Bqnf9r47PA5kp/5NeLJNdXjI7r7u12ffPSVqOsD/J3Vk26nyPbXSX9FO+qvU/673Gx/lfRX+uO+/mrxlWv+F1/5K/Cg35SqH7rU8RFqfzcP7pxqr/TPubfu8RODz1u/NuyKR3Or++usH813GDY6eFBon9A/VcO5PsA3njxMK8n292SoU0k/+qPvO350fkbDzX/0R5Ld9bP7k30C9BW1P3X89Zqv8U8DUPeNnvtDQqa+2rs6rqytHh5V8n+q7qf4bP/c/DvUXo1HB5c7791KrZvi0bzq/rrqR/PVe7RdOll7Wfc9slsd1/oAf+f2E6Dv6mTUyNJXn6B8pzOjofa/8rssM9aXpV/9hOUItb9S8XcDUPeNHcPPfXq7659+GHZ5SF+NP32/mv/Mmiu1q/s/VTcaPzn4zBrsdI+vXB+9h6taT6+v/D39hH40X71ny9Te4c19z1y1tkN96MXtv9PzaW3Xoaf6d2o+1X4y2Fup3/l3gmbozw4Ud+vvHni6+Ssdfx2Aum/kGH761j/x4N5loDvzvuMwbHUopYjvHKq59TuzZjQe8fzpmqf//k8O7KrHV6/fdZ3aXzf9aL56j7ZLJ2sv675HdqvjWh/u5OnnTvWj70p/72q7/fh7tD+Z/X2yPrX/SH72+nbor5ww3am/a+Dp6q9V/PeHi3aj3sxm56v13eu7PcQr4zsGclX8Z3tZqV3Fv+O9H8VXPUfyO31tOBGfuW7l+4q7v5v0o/nqfVem9g5v7vvmqrUd6kNvOAn6rHZGHU6C6v27ndTcpT8aiD2pH9GPxj9d94rKX6v47zcJu1FvVNXDwWz9jIfUnfVPrk/tZTZeffhVxeen9x39VxnQPomfHg6vXOfcvxPxrOueXq/210U/mq/eZ+3SydrPuu+T3eq41gf4+Xn+OeQk6P46O/Sj/cns75P1qf1H8rPXF9Hf9SPlq/qnfuRd5a9VPPuvwKs3s+rhZLa++qFNHXfxsRJ/OozLqh+Nz/hXx6v6nx2GuscjA8kdw8wn19wef3pNVn21vw760Xz1vitTe4e3LsNQt9oO9eEuOAn6rHZGHU6C6v13Owk6GojN1F/Rj8ZfcfPXKp45AFVvVNXDyWx99YOWOu7gYTX+dPiG/5y4u//qw/FT8c5fI6rER3T3564fzVf7y9ZX56v1qQ83sXpS7RRqf+5Dz2h/svs7Wp/a/8787PWt6Gf35/QJ0FfU/lrFs0+Aqqj+UFD9oZD4s/fdBm+zsRX/Tmt7dy3+iBNfw2EgHc2trh/Ndx4IOnjrPPB0GAY5eIC+PBmqKFH7Gw0U1PrR/mT3d2XgddL/7v5HTzju1s/uT7S+m7+r41kDUOVGVT38y9ZXPiASX4+7D7dmY7P+VUPRbO8Zue7+iBMf8TSvG8WpywAAIABJREFUuj93/Wi+w9AxQ3eXr64DUZdBpIsP6EX2SccoKn8nT0hG9HeeJN25xpXfVXnSv/oE4in9rP7sqq/yR/xvZAxAGX7m6e/25/KQ2jX+9H03f7Ox3frR+IyfFe+r9VdiTv6IE1+5fkbT3V83/Wi+wyDSwYNC+4T+qRqrOHsDLxh65v9OUfXv1MzMf9V4xzfdE/5P/07Q2YFnpn5Gf3bWV/gj/iW+ewDaeSOu1t/hb/fDI/H4+4ph1M6h2qmhXSQe9Z1RfyXHyR9x4pHrd12n9ueqH813HSq6+Oow2HSq+4qLD+jDaCih5oS/p/+uVutm6kf7s6O/kfWd8B/t/+wAc3aQl6m/oz+Z9U/4Iz4R3zUAVW9K3YeX2fqnHxKJz8VODCKfxncOYXcMFtVDz1GNk0Nmd3/Eic9ct/J90d1fF/1ovnrP5+JBoX1C/1SNVZy9gTecBOUkaNTHrSdBRwMnB/1If07Uz/RHfCK+YwCq3oS6Dyez9Xf6U3vpFncfPu32ka3/JK4eQGcPmEc5K5oZA1bid8Szrnt6vdqfm340X72fy9Ld5avDYNOp7isuPqAvo6GEmhP+nv474yToc413dDsJuutH3jP1I/05UT/TH/GJeHQAqt6Uug8ns/VPPRwSn48/HQZm1R/FZ/yN4tn6q/Edw9BT9Udxd3/Eic9ck1Vf7c9dP5qv3vO5eFBon9A/VWMVZ29QC06CchI06uO2k6CjgZOT/kp/TtbP8Ed8Ih4ZgKo3oe7DyWz9Ew+CxNfiT4d/Xf2p1z8zfF2NnaiflXvKH3HiHb5P3qxPPvrOuPsDL24feq6eFHTUVww9o+vL9r/T37v8Svor/TlZP8Mf8Yl4xl+BP4F606d+qCHuGVcP/mZjK/6U+jvqn+z96fqjuLs/97iztydxFw+Z2kp/7vrk5+4d1fvSKjUiuPsDb0YPzGqy/d2u3z3/yVAN/Tr1iSfGVwegyk2o+iGE4adv3Km2evi24qWS/mytU0PXmfeisdW4uz+XeOd1u/nI0lX5c9cnf0/+ad1T+qfrrOLuD7zIPqEYpdMJTUf9W/JXT5p213ev7x5/vcbNX/oJUIafOn3iXnU/xWeGb5H4ruFNNf0ZzYzPzIxmdm+juU7+nOOd16yq9fT6k+tbub7ynob83PxdGkp919pPcPcHe8n+nZZRKv/OzQr6N+dn/07QivozA2dFfff4u+t+fXg/o77VCVCGnzp94vN5Ck8zw8/da97ht5r+t/jMvYjEo+te0c/QdPKnjp/6jJ6O777u9ED09PpO6Z7SJ39P/mndU/qn66zi7g80PP1cqH70Pdvf7fo357/LXT1xWlV/ZaB3sr57fFbHzX/KCVD1ZrP6cFJdv2vcbUjxdOAWqT87uNqh5ar/VE89QFypf+qzszvH9WvFk/iJz6ZTXFnzyXWK9c1cF/m+76BPfl7+Lg2lvmvtJ7j7g1w4CXq3/s35FU9q7tIfDcTU9d3jn657vf7Xm/d21D8afzIAVW8kqw8f1fW7xmeHFdnxJ0O33fV3D6Gq6z+tcWpItmvgmDWEc/enjD/9rFaJz163q+4uX6r1Zdc/pU/+nvzTuqf0T9dZxd0fePD0c8JJ0J76N+e/y3U7qZmlHz2xmV3fPf7Kp+tb/Ej8aACq3mxWHz6q63eNuw8nTvjbWaO6/hO9E/fgm062fjTu7u9E/IY1jnDw5ry+7Pon9MnPy9+lodR3rf0Ed39wFk6C3q1/c77zSc0s/dFATF3fPf7Kp+s/DT3V/qfi3wag6o1k9eGjun7X+MxgS+HxhL+nNdDX6q/GXD7j7v6y4t3XN4PaX/X1ZdeP6pPvna/Wd68/wt0f7GX1JNopsv3drk/+99zu+u713ePf+M/L9aNh6Ep9qxOgKtSbRvf6t8dHuA9nlfozMYX+zoEd+nvj7v5G8dncaut7ElfXdl5fdn0HfdfaHfJ3aSj13euPcPcHuYweiNVk+3sytOmsT/733O76p+vvGDjO+M+Of+NP7uvgU+k/5QSo+yZc+ZCnru8cP117dkBReTiUufanWpn6u+/bLn2H3uzWj8az792u+ErM6WvKibi6ruv6suu76LvV7ZK/W0el715/hLs/2MvtJ0Df1cmo4a5/a/7K76rsqJ9Vf9cJS6eh59P+fDrxqRx6hu7PuwEow0/f+q5xRd2ZQcXpdc8MiDL1dw2Hdurv0M7W3zEcc9QfxWZzZ4eFyvhsT5y+1u2Mr1x/0pPj+rLrn7x+JV89TKyer9I+oe9ef4S7P/gnt/9Oz6e1HYeS2fo35Hf7nZ079TPq7/yRcvVQM9qf18Hnp/dW668MPbeeAGX46VvfNZ41bJmtedLfDl+Z+juHN5n6T4eVav1oXxz1I5ozfXX7urTr30BEzymuqrfruqyB4e7rXPV35ZGfo6PSd68/wt0frPH0vqp+9F3pbzRU6K5/Q37k89Vdf0f9jBOe365VDz2jA9xT/j7dh9n78/YEqHrTuPshZ7e+e/0OQ4an8Zl7keFvx0AwU3/nACdLP3uAtkN/x3120x9pzdaa/Sxkx7M/+6OcCvGZ6xTfN53Wl11foT+T774vdM9XaZ/Qd68/wt0ffIeToM9qZ9Rx178hv/JJzWz9SP3QQO1B3dcc9dBzpj+/3sRP+5s9eTo8AareJFYfPqrrOw0/3b3srp89fIrq7x7i7NDPHqJF9XcMJqvoj2JP9Xbr74jPeJzR6RZ3q6Ouu9uf+/XRPPJzdFT67vVHuPuDPTy9z5wE3V/HXf+G/Mjnq7t+pP7OH3l/d/2nHPXQc7Y/3/qS6e/Xy+vX68O/A3QG9XAwW9+9fvXhwo54dECzGs8eAkX1d/YgQz/7Hu3Q3zGsrKL/JP6kp+5fCzJqdIqfqqGsrfTnrn+qRud8lfYJfff6I9z9wRycBH1WO6OOu/4N+ZVPambrr9SfHXgq/StOgn4aNp7yt/0E6CrVN/rV61cYfio8nvD3tIZKH38x/V2foer6n+J8DSC+Kz6iu7/b9bvnd9d3rz/C3R/8k9WTZKdQ+3MfSmbrd8+Pfr5u0l+pv/sE6Lf6jkPPmfWf9vefl/dGw9BPGv8jegLUlexNfXZ99/gI9+FsV/0d/mZiT4ZPO/Oz9d39ddePxt39Ec/XVvq7XZ/8/vru9Ue4+4PvjAYSatT+suu763fPfzK0Qn9f/PWaav47xX+9vPf6elp/dQDqvMk88RCVWf/kgCAazx6wRHKrDX/Uw7vZ2JN6mfkz2t80MvLRj+lH47s+W8R1A9Fduip/t+uTf4e+e/0R7v7gn6hPWI5Q+8uu765/S/7qSdPu+rtOcO748fGR7rtc4p/jnwaf7655pL8yAHXeUDL8PKe7cwC429/MAESp//T90/mZ2ie9zby3I38lhv6++JPPluvX6FviK9fvrKnwd7s++ffqu9ePUt3/brJPPKr7HfmdfxXqu+vfnK/+nZqO+jt/ZD3jd2a+4jRQrBb/NPg8dgLUebPI8HM+b6emi7+Z4aRK33WAN9uPmV5k5H9736VvO/4N3agfiWd/DSC+L56lt/u6bN0u+uTfoe9ef4S7v6qc6qvqR9+fri/LX3Z9d/2b80dDuRv0M054frs2+jszZ68n/jkuOwFafTO5+yHtdP3dw8VIvZVenfL3dPCh1FcP5mbyn15/Ov+p7uy1XYeJVfSj8SefK9ev1bfEZ67L+L590t/t+uTfq+9eP0p1/9lwErR2fXf9m/MrntTcpR8dqI3qvuasDEVn6joPHN3inwafqSdAq28UGX5qaq3kOA8/MvRn+6nOf6pxMv/b9U+vzcpfiaEfjz/9nGfVJ/48flonW3d1P1Fdn/w79N3rj3D314VTfeYkaE59d/2b80dDuc76O3/k/d31n3J2nQR1GihWix8/AVp9M7nroUpVf9dAMtPHSizbXwX96JDmZP6T6x16+E1HnY9+TP9J/MlnyvVrNvG9GpnamfU76JN/r757/SjV/Z+Gk6C167vr35zvfFIzSz86UHsl4k/h/+b4r5f3Xl9P638bgFbfKGY+RJ2ov2sQsTq8jMZV/iroP9UgP5b/KabORz+m716fuE98RPX67vrk363vXj9Kdf+7WT0pVgX1+rLru+uT/z23u/7uE6Df6o/8KfzfHD9+AlSF+qFkhPqhTv1Q5OovOljL1r8hf2bYdFs++rlfW9T1o3Fnbzvip2pkamfW76DfOX+XRmd99/pRqvvPZvRAWx31+rLru+vvzp/V2J2/e/3Z/k7rz9Z383dioBsZ2qr9W54AVW40HR7CMuuffEBWDyBG8egAaqd2tn73/Nn7ViX/aZ92fW5n9Vc+09HeqfVnamfWj8ZdfZ2In66TpZtVv4t+1/zdOl313etHqe5/N+oTktmo1+d+QjNbf5e/Jz++nJF/av3Z/rL0d9VX+Tt1gnXX0NNtfTMnQP+8t7y+dwNQ5cbyxMOQs7/MIUek/iie7W9Wf2YAs1I3Mjyrnl/Z+9P8SD9G7+/Q3zVU2/01Ilv/Wzz6uVDHV+9ph3iW1tPrd9fcXb+zfod8lXYHfff6MEf1+xX9nY7u9d31M/2dGHoq15/tL0N/ZiC8Y+i529/JH9nPGHqq17dyAvTv7/96eN1b/dcBqHIjeeIhy9mfYpCyI57tb1b/6SBkpWZ0QNUtf6bXlfOjfcvWV/wbUerPvp9RPxpfuZcd4ruvm/2eftr/rfpd83frdNV3rx+luv9sqv7o+9P7mrW+7Pru+pn+RkOjHfnK9Wf726G/csLxaf0T/hQnPL9d+3T9n3ycXt+OE6B/fz+0vt8fLpolO//UQ5mq/s6H5JW1uvqL1s0aGM1e2y0/OuCqkJ85gNyhv8PL7LDQTf/b9Y4Dxtl+qL8vKr7v7tJ8cl3mvmFH/c76HfJV2h303etDjOr3j5OgWn1Ogq7nO58EHQ28dtTP9Jcd/3Tdp5zZ9X/6MfLR9Q7xd55D6/v9RmCW7HzFQ9jJ+juGHitaGZrZWqeHL9HhSsf8aJ/d87OHbFH9HUO0p5+TUUylP1ND/f0l6r9LfPa603Wz/N+m3zV/t05Xfff6Uar7Pw0nQT3ru+tn+hsNjXbkK9ef7S+iHz2R+aR+pr+TP/L+7vpPObMnQav/SPzo/Ufri/4V+OxN6amHMlX9Ew/HyuHDqoeo/g5/0SFO93z1PTqRPzNs3PEZm9WfyX8Xm81301+pofyes8N/h7i6ttL/7fod8lXaHfTd68Neqt9PToJq9TkJup7veBJ0NPDaWT/DX3b8ld3rr3wC9DUeXl9kAJq9CVU+hJ2o/3TQkfUw7+pPPfib0bg1391fNH/XZzzr35A6v7t+dry6/53xEWp/av+367vno9+7PtzF6km5KvXd9U/6W9He/TsnM/Oz/a3on6yf4e/0CdBXoj/+/m4w+GlY+EnDJR5eX/QE6CrZm+oo3R+qXP25D8Y65M8MW1YGgxF9dX5UP1rfPb+7fjQ+m+vm/0Q8W9vZ/+36HfLR710f7mb0QF69vrv+k6FVhN3+ngy0Ij9eX81ftfqz/rLjI3YMDJ/+uPg3jadxxUD08fpWB6DuDzkRnB6g3R6Qdw9gMrXJX89d0dutvyv/aR9X17fy76lK70axrvo7tKOfxyexDvFsXVf/t+t3yUe/d324C06AeuhHT2o+0V3RXvldkjM1qvtzr7/LX3b8Ezt+ZPz1vd3+VQPRT4PPd9f8/PysDUAjX4x2PGxkfkNSPQyuDmlOxaP+ZuvODDgq5z99P3JfdtdWru1p/s5+7RxY7dY+nd9dPxKf/Uzs+lpZLb5y/c6ajv5v1++cj7431f3fRvaJzOzPQ/R3QnbW3zH0jPrL/J2MUf1ofoa/mYGtov5OfyeGnpnrfzcI/DQcnPU/qvMkLxr/9fLe6+t/5c8OQDM3wtkb8aj2yQdn9wf7XQ/0kQFe5fxTvZ5Zy0p+tv6u/NW+RvQjddzzu+s/ia98Fj7FFf5d4ll6u67b7X9Vt4t+l3z0e9cHT059LrIGrU/9r9avrD8auqxqvOOTbiT/if/q/lZOUJ6sn+kvO/7pmpnrn5yQ/Pv7u/x/8jF7fyxPgGZvNnc/hMyiegicfRjeXX8Uj/qLDg865s/0NHtwt0vPcYAX6fOq/rf3o7oO+d31Z+uvXD+Kqb4XnYrPXJexL3Dyf7t+53z0vanu/3Y4CdpXn5Ogvv5GAyl1/RP+lCdBI+v/Nix8kvMkPnuy1P4EaPZGMuMhZwbVA2HWUGVX/MSg7lvshvzZ+72rRmQd7n3e/bnd7feppnN+d/0n8RkPMzq3xU/rqOuu7meq63fJR793fajBqc8JJ0HP64+GLqsa73A9aflNP5of8Rc9sZhd/4S/7Pina55c9zT+LS/i/9fL69frS50Azd5s7nr4WEX18LfrAd7VX3Q4cEN+do+z11Cxx9/qZOtHNF3yu+vP1s/woPqe5BLfpZGlvcP/7fqd89H3prp/+CecBO2rz0lQP3+jgZS6/kl/ipOg0fX/+hDb5b/NCdDsjWTmQ84TVA960cFXtP4onj2YI/95jV3Dumr60fzse1jhM5aZ313fvT7x5/ER3f2563fPR9+b6v5vY/UknQvZ/jvpr2hH/e3MHw2NovrR/BV/7vVP+suOv7smY2C4K/6fl/eGJzAH/mbj20+ArqL+pqR+qFE/lKj9qfK7DOYq688Mg3bnV9d3z++u716feOx7x5Nc9++t1fW756PvTXX/tzN6IHYn2//t+u751f09Gdop67v5uyn+afBY8gToKjseMjLrK+PqB+jZ3EoDGnfv3fVnP3dP85+uY9Wfq75z/qzWzq9JJ/RnamfWJx6PR/NU/m7R756PvjfV/d8GJ0DRr5zfxd/qSd1T9VX+bo5/OnH5TUNaf/cA9MkHLfMblvphS/0wkjnEmNVX5s8MSCL5u/xE+hIZEO0Y9O3uzazmij9nfef82distlr/Wzz6uSd+Jr5y/Yymwl+n67vlZ+u7+6te353T/XE/UZndj+z1j/xH60f7E/mdiU/yR0T13fJnB5bO/p4MFZX1Ff6Iv489PYUprb9zALr7QWN3fbd4dEj3NL4yzKg0lIn0dWXgNePliV72WjP1o5/hyFBpV6+d9Z3zZ3RWvgYp9Wffz6hPfE989fpd163ueXb7dNXvmo++N9X9Z1GlL1mDylPr/+R/V/3V/jytr/J/yt9q/soJxhn9aH7E37trowPenfVP+CP+Of7kx9Ffr5XW3zUAzXoIOFVfHR9x4iH+aa3Rtcr8yEDqaX7mQPHpe+rBXnQAVaXPKn3X/G/vv4ut/Pt20v92/Up/iJ/7XvrkupXvyyf9ddTvkp+t7+6ven131P3hJGgus4OY3fojOAm6lj8a2ET1o/k7/EVOWp6on+mP+Of4rzfvf7r+3bXH6+8YgO56IFDVV8SjA6Qn8Z3DgQoDmuhwZCV/p9/Ma3bqZ/fZ6XPhoO+c//T63V/fFF8/Z3pI3Ceedd3T63frdbu+Wz763lT3f4oqfeIk6Jr+iKf1OQn6/fpRnYr+3uWsnuzMqJ/pj/iz+Oj97B+Jf1Q/OgDdvfk/XV8dH5H98P4knjGAyM6Prn9lqJU99IusMUNf0eNvdbrpV8t/F5vNd9NfqaH+nkJ8TOT7ctU9QRV99/xsfXd/1eu749YfToLmMvLPSdCY/un80cAmqh/N3+lv5aTlyfoZ/og/jz/9kfRdJ0Cn60cGoNkb5ez6qvjTwVG2/mqN7MFbNP+Ev8g93KEf/QxF9dX1R7Hu+t3zu+sTrxMfUb2+u757fra+u7/q9d053Z/ZE1rdUK+/ev1s/2p/0YFRVD+av1P/Xa5T/Qx/xJ/H38V+vbn23XtH6n8agKq/6akfenioefb+7sFXNL+7foX6M8Mi9Mm/Sd89fuL7nvP6n1C9vru+e362vru/6vXdUfdn9MDZHfX6q9fP9q/2Fx0YRfWj+Wp99/rE8+K/Xt57fX28/rsB6I6HmAjqB6CTD5iVHuDVwxH18Emt715/drCyS/9pHzL1I/dmRb97fnd9l7hyXafq7PLZtb67vnt+tr67v+r13TndH/UJRDXq9VevzwnQXP1ovlrfvT7xvPinwee7a47Ufx2APvkwZn5BVj/sZMVXhzSu+jMDAkW+i/7qcEVZP7P2Lv3M9c7or2qt6n/L7ZDfXd85PtOX3fGTtWb3L7uvV9c/fX21eqf13f1Vrz/C3d9p3PuRfSIyuv6oP/f6I/1o/ojRwEOdr+7faX+7B56n6xM/F//18t7r6+P1f78JfiPzm6P6wefkg+XOB+vT+k8HYE997M530M8coJ2sv7PXmfqr/wZW9VfvQZd/Y9n53fUd4rPv74rvvu6U39U89/oq/Wi+qu4pfXd/1eu7o+6P64++n+rL6vp3+XOt/1Q/mv9Ud1bvVL6qf9n+dg08VfWJ6+K2J0CzNulPUT/knHzIXOmli35kIHUi30G/wuAt+3W2n5n3MvUjn7cV/W81OuR311fHo/dzd1xR86mn1T2Pe/1T+tH8U3VU+u7+qtcf4e5PjXt/OAmqra86CToaeLjkq/qX7S86kFLXJ66L/3p57/X18fq/3yS8ot6oVY1HBjSO+urhSNbQLFN/55DrVH33Pkc/9xn3NfpvdFV/Vc89v7u+Q3xmjTvis9ftqrt7f3Paf7frq9U7re/ur3p9d9z6w0nQNXb5c63/VH/3SdAqJ0A/XT9bdzY/29+pH3nPqk9cF7c9AfoJ9WasanzXA7SrfvThOTvfQX+nR0X9ij3+5uNED2fq79bvkN9dXx2P/hvaFc/UVnrvUD+qH81X+8vWd/dXvf4Id39uuPeLk6Da+qdPgo4GHm75p/uX7W9m4ORYn7gu/uvlvdfXx+t/G4CqN2pV45HBVgX9aP3sfAf9XR5X+xetn+n/ZP1Pser1o/ru+d311XGn9Y1w7N8M1etH9aP5an/Z+u7+qtcf4e4P/kn0BF82an/Z9audwLwtX63vXp+4Ll7uBOgq6ocSdXyEq/7Mg3GmftbgLFu/u78n/meGKdQ/q++e311fHXdYXzRX3d8R1etH9aP5an/Z+u7+qtcf4e4PvjN64FWj9pddP6pPfm6+Wt+9/un46zW74+r1tT0Buor6AaXzA+buAUVUe6Z+9eFR9eGZYj3fYqfq76idXV+h75w/q7X6NU+lfyoevTfR+k/i0Tx1/0dUrx/Vj+ar/WXru/urXn+Euz/4J+oTliPU/jgBene+Wt+9/qn1nRp6uq3f9QTo20Ho7gGo+mFEHR+xqn96yLAyEJqpPzNgWMnPHI5l+NnpT/169v2M+js/h5n1FfrO+bOxWW21vjIe/Xe7K75y/Yymo/9K9aPXV6uHXu36I9z9wRyj+6k+Ear2NxoIqPWj/emW3/0E6CvR/rjFP1377rqMoad6/c4nQL/V/vn52XsC1PXBTxV/+sA5imcMJWZ0dtef6cuOgZHbgOykP/XrV1T1d92DXfVd9J3zZ3Rm89X6J+LRexWt/zS+ev2u63b7n81zr787bzb/VB2Vrru/qvVHuPuD73wapLjcV5U/9xOgn3Rm9armq09IntLP6o86/um6T9fuGop+8unWH+UJ0KnTnz8/Pz+7BqCuD4Eu8RG7HlYjuu/e310/MtBZ0Ts9eHLxp349ip2or7zPM+8p9F3zv73/Lrby9Umpnx3f0edT3wufXLfyfdXJf8X6Uf1ovqpulk6Wbpa/KvVHuPuDGKP7e+tJ0NHAwEU/2p+q+dH+zQyElPpZ/VHHP133KefddTtOgv5689636x3iv17ee329S39U93/v7RiAOj8QquLRAdFT3dlrsx7+dw2bIv5W9Hdds+Iny5/69af3nPw8/feZ/bl20HfOf3r9ytdXpf6JeOQzfjKedd3T63f5z7peXT96fbV66NWuP8LdH8zBSdDv9XbX2a1f9STnar7LCc1T+rv7o46/ojoJWvFH4k+eAP3Wp/+9Fx2Aqh+e3OMjog+vn2Iz+U+u310/21/GUGymBw7+1K9fUdTfkbNby0W/Wv672Gy+Wj87ntHj0/Gn12TVV33Pr1I/qt89P1s/2597/RHu/mAvo/t920nQ0cDATT/an2r50f7NDIQc9Hf3Rx1/5dv6Mk6CcgL0uf6wRmQAqn7QcI0/HQxF8z/F3PMd9Hd5dPWnfj37vtvrUay7fvd8tX52vLr/mfiI2/1l14/qd8/P1s/2515/hLs/2Ev2Ccgo1U5oVtN3z0c/pq+Oj8j+8fdfb6599943DVX81AnQV/1P730cgGZvirOp/tBTNX/mwTtDn8Ge9vXs+26vP713i373fLV+dry6/yfxEbf7y64f1e+en62v3nur649w9we5jB6Y1WT7u13fPR/9mL46PiKz/usQ7/V1dv1oPNv/tP67AeiODbJyE1n5AbRy/kruTv3qw7Hqrz+9p/KzYz2f3l/5bEf0d/67earXOV+tnx2v7n8mPuJ2f9n1o/rd87P1s/251x/h7g/2wgnQu/Xd89GP6avjIyqfoMyOq06AftR/HYDu2Awrv+GoHnZWhySzZOVH/e8a2OzSdxqOdXv99JrVXPXrT+89vXan/ul/R99yO+Sr9bPj1f0/iY+43V92/ah+9/xqetXqv+LmB7SMPg/qE6GjB/rd+q9E653273aCM5p/Wv+VUT03/+r4K9n9+xb/9fLe6+vs+tF4tv9p/d+fAm94stFQbkZUDz7RocSn+NMhUzQ/e6hyWt9pQNb99cx7J/xkrO/T+0/eW9VX9322TrV8tX52vLr/mfiI2/1l14/qd8/P1lXuuR3qf8LVF5zh06BC/bk4dUI1a/2cAI3ln/K3ev/V/tXxT9eNrt9VnxOgB/V/v7ngHdmb5Gyy1jczhIjUz8qP+t81uNmlf2IgtmPwVf31t/c79GfmvUz90/fhW40O+Wr97Hh1/0/iI273l10/qt89f7fOKd0q9f/g4gM8mR1UnGL0QJ9V55XVutn+o/ru+af8rd6H2MASAAAIHUlEQVR/tX91/NN1r2T171v818t7r6+z60fj2f6n9X+/EXwle8M8S/SheVc8MiCZ1cnIj/rfNfTZpa8YilXwfaIPkVz315/ee3ptRk9mtCL6q3ru+Wr97Hh1/zPxEbf7y64f1e+eX02vWv1X3PyAFreToKdOgH6qF63LCdBY/ml/s/df7V8df+V0/zgBelD/01+B/1R8Nq4m6+Fi1wOmanOePYBQ6yuGYre9HsXU/jLu8af3dumvejqh3yFfrZ8dr+7/SXzE7f6y60f1yffWpz50YvR5OX0SdPRAn13vldMnJLP13fNP+5u9/2r/6vgrp/v3Lf7r5b3X19n1o/Fs/9P63wag2RvlXcwMBHbEo4OhXfq7BjGnB1vZ+orB2G2vZ9/v9noUq14/qu+er9bPjlf3PxMfcbu/7PpRffK99akPnTh9ArNbfU6AxvKr+3Ov39k/J0APnwDNInvTra6v1o+yOtjK1ue19vXs+91ef3qvS/2ovnu+Wj87Xt3/k/iI2/1l14/qk++tT33ozOiBmvq5+dn67vnV/bnX7+z/18t7r6/V/kbxbP/T+ooB6M4HvAyNyg/gmQOQldyd+rzWvv70nsqP+vW393f0dmd9hb5zvlo/Ox79txutPxPfpZGlnemtQv1ojRP+nPN3aSj1XWv/wcED+PNu2HH6s6M8Abpj/ZwAjeUr/T25/5n+T9RX+s+MPxn0vdNw6W+2/yX90wPQ7M28uv6q/qkhwYin+qvDsl36vNa+/va+2tuJtWf+O9lZX6HvnL8S26mfHY/2Mhp7Et95zUpednyEe/1ojRP+nPN3aSj1XWv/wcED1GP0MN3dw47a2f6j+u75Sn9PcjP9n6h/yv/pYeivN+9/e2+kcyL+1Ouq/7D+yQFoxsZ/ZiO0u/7TIdEorhoKrA65Vgcou/R5rX396T2FF/XrSF7mv1EXfef8XTVX9bPjK98vFetbuVb5fT/ipWL9aI7Cn1P+Kc2T+qpaT3H0BPuJDt8Un5OTJ0BH/VmpzQnQWP5Jfyv3f6d/Rf1T/v+ed3IY+uvLe++0TvZvx9D2qP6pAah6kz9C9ZCx8pC6M39WPzIc26HPa+3rp+91f/30vSc6mfVV+q75394/oZ8dz17zSOtJfPba2e9nyv4/wb1+tNZJf475WVoKfZean3DyAudZvf+Kk59/GA0UMmvtqJntP6rvnn/S38r93+lfUT/bv9uPwX97/51eZnzkKep/u/6JAejpDf/Mw+pq/aeDjW8x5YBgZQCyOjTZpc9r7etd13R7vZqzU8tZ3zl/JXbi+8uu+EzfvsUy1xfJeXKdsv9PcK8fzVX6c8hXaZ/QP1VjFWdvsE7Fk59/OHECNOPk5yft3f7dT3BG80/4i9z/Hf6V9bP9//16hx+Df5pzIr7yI+nfNNL1sweg6s39CNXDxdMByadYNH/EjqHQt5h6aMXr2GsHD+rXu3J2a7noV8tX62fHV/Iy1hfJ23GNek/iXj+aq/TnkK/SPqF/qsYqzt5gH6OHcmdOeM+ske0/qu+ef8JfpMYO/8r6J/3/GlyTFf+Wp+zfr8n35PqZA1CXB5GdD4Tv3p/Vf5r/KZadv2MwpKzP69zXDh6qvx7Fuuu756v1T8WzvkfNxKO5zv19gnv9aL67v+x89LW4+4M5ngwsnDl9gnJ3DU6AxvJP68/WODFQPFk/w7/Lj8E/8XYy/p+X92ZPZx7X/zQAVT80ZG+qR6g3zVn5Mw+2ivq85nXn15/eu0XfPV+tr46fWN8I5/48iY9wrx/Nd/eXnY++Fnd/EGP0wOxOtv/b9d3zu+u719/h/5cwrl7/t/ivl/deX9vpvxuAqh8gdj7A7dZQP6BH8ldydw5I1MMnXvPa6fW39zP/3Sn0q+Wr9dXx6Pel2XgkVx1/eo1r/Wi+uz+HfPS1uPuDZ3Q7/fnzs9f/7foV8iMnQCvou9cnnhN/Mqi0038dgKofJjIfpJ6g3ihn5e8ceJyo3+m1kxde619/em+nlot+tfyV2E59dTza65n4ap46/vQa1/rRfHd/Dvnoa3H3B2uMHpbdyfZ/u36F/EiNCvru9Ynvi49+BP3bexb6vz8E3lExPrMRmtV/MjxwyM8aemTV7/TayQuv/V5H8ma+3p3W75C/q+aqvjq+8v306ffbzO/Lp+PRHHX9aL67P8f87vru9bPpvr6nZA8o1X2Ori/qf1S/u37F/JkTkhX13esTX4+vnM58qiHR//3mondUj0dRb5hX83cNPlZRD5iUrz/9N695/fS9JzrO+tXyv71/Ql8d39mTp/Hdeif3EivfK9X1o/nu/pzy0dfi7q87Wf13Ofm5ur5d/j/Vv0W/Yv5rzsy1FfTd6xNfj/8avD9zrYX+7zcJr1SPP7l+9gHv6eBAmb9rKLNL66bXn/6b17ze9W/1qZZCv0v+Smynvjo+09eZ+Mx1zv0ZcWJ9WblP8939Oed313evn0339X2Ck5/fifp3P5mZrV85/8kJycr67vWJr8dXfiT9m4Zc/9NfgX+XXDEeRb1BXs3fMZSJeFAPlZSvn/w3r3m9K8dJv3O+Wl8d3/G9eMc16ng0V10/mu/uzzm/u757/Wy6r+8po4fq6qjXl13fXb9y/pPcyvru9Ymvx39Nvmev/20Aqn7QOPUgM/vA92SIoMzfMZj5FlMPjtxfv3tf7YnXvV6PYmr97vlqfZf4ju/Bzut7Eh/hXj+a7+7PPb+7vnv9bLqv75UnA4/KqNeXXd9dv1v+a243fff6xJ/H//Py3uzpTDv9/w8pzzdL9T1QBgAAAABJRU5ErkJggg==">
                    </image></defs>
                  </svg>
                  <style>
:root {
--step-3-animation-timing: 10s;
}
#old-request{
animation: build-in-bubble0 var(--step-3-animation-timing) ease forwards infinite;
}
@keyframes build-in-bubble0{
	0%{
  opacity:0;
  transform:translate(0%, 15%);
  }
  10%{
  opacity:.25;
  transform:translate(0%, 5%);
  }
  20%,100%{
  opacity:.25;
  transform:translate(0%, 0%);
  }
}
#previous-request{
animation: build-in-bubble1 var(--step-3-animation-timing) ease forwards infinite;
}
@keyframes build-in-bubble1{
	0%,10%{
  opacity:0;
  transform:translate(0%, 10%);
  }
  25%,100%{
  opacity:.5;
  transform:translate(0%, 0%);
  }
}
#chat-bubble{
animation: build-in-bubble2 var(--step-3-animation-timing) ease forwards infinite;
}
@keyframes build-in-bubble2{
	0%,20%{
  opacity:0;
  transform:translate(20%, 0%);
  }
  30%,100%{
  opacity:1;
  transform:translate(0%, 0%);
  }
}
#next-request{
animation: build-in-bubble3 var(--step-3-animation-timing) ease forwards infinite;
}
@keyframes build-in-bubble3{
	0%,40%{
  opacity:0;
  transform:translate(0%, 10%);
  }
  55%,100%{
  opacity:1;
  transform:translate(0%, 0%);
  }
}
    @keyframes fall {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(1200px) rotate(360deg);
            opacity: 0;
        }
    }
    #confetti{
      transform: translate(50%, -250px);
      opacity: .3;
    }
    /* Animation timing varies for each confetti piece */
    #confetti rect {
        animation: fall 3.3s ease infinite;
    }
    /* Randomizing delays for each confetti piece */
    #confetti rect:nth-child(1) {
        animation-delay: 0s;
    }
    #confetti rect:nth-child(2) {
        animation-delay: 0.5s;
    }
    #confetti rect:nth-child(3) {
        animation-delay: 1s;
    }
    #confetti rect:nth-child(4) {
        animation-delay: 1.5s;
    }
    #confetti rect:nth-child(5) {
        animation-delay: 2s;
    }
    #confetti rect:nth-child(6) {
        animation-delay: 2.5s;
    }
    #confetti rect:nth-child(7) {
        animation-delay: 3s;
    }
    #confetti rect:nth-child(8) {
        animation-delay: 3.5s;
    }
    #confetti rect:nth-child(9) {
        animation-delay: 4s;
    }
    #confetti rect:nth-child(10) {
        animation-delay: 4.5s;
    }
    #confetti rect:nth-child(11) {
        animation-delay: 5s;
    }
    #confetti rect:nth-child(12) {
        animation-delay: 5.5s;
    }
    #confetti rect:nth-child(13) {
        animation-delay: 6s;
    }
    #confetti rect:nth-child(14) {
        animation-delay: 6.5s;
    }
</style>
                </div>
                <div class="video-colourise"></div>
              </div>
              <div class="work-tile-text">Receive initial samples in 2-3 days, with subsequent requests queued on your project board and delivered within 48 hours.<br>We&#x27;ll keep revising until you&#x27;re 100% happy.</div>
            </div>
          </div>
          <div class="w-embed">
            <style>
.work-tile:hover .how-it-works-video{filter:unset;}
.work-tile:hover .video-colourise{opacity:0;}
.content.sliding-work-showcase.highlight-subtle-block.how-it-works::before{
	content:'';
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  position: absolute;
  top:2px;
  left:2px;
  background: var(--bg-modern);
  border-radius: 6px;
  z-index: 1;
  opacity:.8;
}
</style>
          </div>
        </div><img sizes="(max-width: 767px) 98vw, (max-width: 888px) 90vw, 800px" srcset="images/blur3-p-500.webp 500w, images/blur3.webp 800w" alt="" src="images/blur3.webp" loading="lazy" class="blur">
      </div>
      <div data-w-id="ae71350b-e303-c965-fc59-bf2e5e30b9db" class="content spread centre-vert reverse-orientation content-build-in-target">
        <div class="w-layout-vflex text-org responsive">
          <h2 class="gradient-text ready">Ready to dive in?</h2>
          <div class="text-block-reading">At Flat 18, we&#x27;re all about making development fun and easy.<br>Let&#x27;s chat about your ideas and discover if Flat 18 is right for you.<br></div>
          <div class="text-block-reading">Check out our pricing details below.</div>
          <div class="w-layout-hflex btn-flex">
            <a link="#chat" href="#" class="btn w-inline-block">
              <div class="button-background"></div>
              <div class="button-text">Chat with us</div>
              <div class="icon btn-icon w-embed">&#xF715;</div>
            </a>
            <a href="#pricing" class="btn sec w-inline-block">
              <div class="button-background sec"></div>
              <div class="button-text">Pricing</div>
              <div class="icon btn-icon w-embed">&#xF128;</div>
            </a>
          </div>
        </div><img src="images/2023-HERO-CANDIDATE.svg" loading="lazy" alt="" class="hero-img content-img">
      </div>
      <section id="pricing" data-w-id="ccc7714e-4111-33db-545c-124e3954058f" class="content vert content-build-in-target">
        <div class="w-layout-vflex text-org subscriptions">
          <h2 class="gradient-text yellow">Pricing. Simple.</h2>
        </div>
        <div class="pricing-details-wrapper">
          <div class="pricing-head dramatic">
            <div id="w-node-d23227f8-aab5-0c14-c064-709c4c129f2f-3954058f" class="content-imagery-wrapper">
              <div class="w-layout-vflex pricing-controls-container small-screens">
                <div data-animation="over-right" data-collapse="all" data-duration="400" data-easing="ease" data-easing2="ease" role="banner" class="currency-dropdown-menu w-nav">
                  <div class="menu-container w-container">
                    <div class="trigger-currency-dropdown-menu w-nav-button">
                      <div class="text-block-5">Change currency</div>
                    </div>
                    <nav role="navigation" class="currency-menu-wrapper w-nav-menu">
                      <div class="w-layout-vflex currency-menu">
                        <a href="#" class="currency-link w-nav-link">GBP</a>
                        <a href="#" class="currency-link w-nav-link">GBP</a>
                        <a href="#" class="currency-link w-nav-link">GBP</a>
                        <a href="#" class="currency-link w-nav-link">GBP</a>
                        <a href="#" class="currency-link w-nav-link">GBP</a>
                      </div>
                    </nav>
                  </div>
                </div>
                <div class="price-wrapper">
                  <div class="pricing-price monthly">
                    <div class="w-layout-hflex price-display">
                      <div class="price-exchange">£2,995</div>
                      <div class="month">/month</div>
                    </div>
                  </div>
                  <div class="pricing-price quarterly">
                    <div class="w-layout-hflex price-display">
                      <div class="price-exchange">£2,495</div>
                      <div class="month">/month</div>
                    </div>
                  </div>
                </div>
                <div class="switch-wrapper">
                  <div fn="billing-type" class="switch-body billing-type on">
                    <div class="switch-indicator"></div>
                  </div>
                  <div class="w-layout-hflex switch-label-wrapper quarterly">
                    <div class="switch-label">You&#x27;re saving <span class="price-exchange">£1,500</span> with quarterly billing</div>
                  </div>
                  <div class="w-layout-hflex switch-label-wrapper monthly">
                    <div class="switch-label">Billed Monthly<br>Activate savings with quarterly billing</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="w-layout-vflex flex-block-3">
              <div class="w-layout-vflex bricing-details-wrapper">
                <div class="badge subtle left solid">What&#x27;s included</div>
                <ul role="list" class="pricing-feature-list wide columns w-list-unstyled">
                  <li class="pricing-list-item things-are-different">
                    <div class="icon small w-embed">&#xF26E;</div>
                    <div class="pricing-feature">Queued Tasks delivered in as little as 48hrs</div>
                  </li>
                  <li class="pricing-list-item things-are-different">
                    <div class="icon small w-embed">&#xF26E;</div>
                    <div class="pricing-feature">Unlimited Development Scopes</div>
                  </li>
                  <li class="pricing-list-item things-are-different">
                    <div class="icon small w-embed">&#xF26E;</div>
                    <div class="pricing-feature">Application staging</div>
                  </li>
                  <li class="pricing-list-item things-are-different">
                    <div class="icon small w-embed">&#xF26E;</div>
                    <div class="pricing-feature">Unlimited Revisions queue</div>
                  </li>
                  <li class="pricing-list-item things-are-different">
                    <div class="icon small w-embed">&#xF26E;</div>
                    <div class="pricing-feature">AI &amp; Custom Graphics</div>
                  </li>
                  <li class="pricing-list-item things-are-different">
                    <div class="icon small w-embed">&#xF26E;</div>
                    <div class="pricing-feature">Complete Service Management</div>
                  </li>
                  <li class="pricing-list-item things-are-different">
                    <div class="icon small w-embed">&#xF26E;</div>
                    <div class="pricing-feature">Support directly from your developer</div>
                  </li>
                  <li class="pricing-list-item things-are-different">
                    <div class="icon small w-embed">&#xF26E;</div>
                    <div class="pricing-feature">Pause and resume week-by-week. Bank all your unused time</div>
                  </li>
                </ul>
              </div>
              <div class="w-layout-vflex bricing-details-wrapper dark">
                <div class="badge subtle left green bold">Billing</div>
                <ul role="list" class="pricing-feature-list wide w-list-unstyled">
                  <li class="pricing-list-item monthly">
                    <div class="icon small w-embed">&#xF1E2;</div>
                    <div class="pricing-feature">Monthly billing</div>
                  </li>
                  <li class="pricing-list-item quarterly">
                    <div class="icon small w-embed">&#xF64A;</div>
                    <div class="pricing-feature">Quarterly billing</div>
                  </li>
                  <li class="pricing-list-item quarterly">
                    <div class="icon small w-embed">&#xF247;</div>
                    <div class="w-layout-hflex inline-text-blocks">
                      <div class="pricing-feature">Save</div>
                      <div class="pricing-feature price-exchange">£1,500</div>
                      <div class="pricing-feature">v monthly</div>
                    </div>
                  </li>
                  <li class="pricing-list-item monthly">
                    <div class="icon small w-embed">&#xF117;</div>
                    <div class="w-layout-hflex inline-text-blocks">
                      <div class="pricing-feature">Pre-pay</div>
                      <div class="pricing-feature price-exchange">£2,995</div>
                      <div class="pricing-feature">every month</div>
                    </div>
                  </li>
                  <li class="pricing-list-item quarterly">
                    <div class="icon small w-embed">&#xF117;</div>
                    <div class="w-layout-hflex inline-text-blocks">
                      <div class="pricing-feature">Pre-pay</div>
                      <div class="pricing-feature price-exchange">£7,485</div>
                      <div class="pricing-feature">every 3 months</div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="w-layout-hflex btn-flex orientate-right">
            <a href="/pricing#more-info" class="btn link w-inline-block">
              <div class="button-text">Learn more</div>
              <div class="icon btn-icon w-embed">&#xF135;</div>
            </a>
            <a link="#chat" href="#" class="btn w-inline-block">
              <div class="button-background"></div>
              <div class="button-text">Let&#x27;s talk about pricing</div>
              <div class="icon btn-icon w-embed">&#xF715;</div>
            </a>
          </div>
          <div class="style code-embed w-embed">
            <style>
	.switch-body:not(on):hover .switch-indicator{
  	transform: translateX(4px);
  }
  .switch-body.on:hover .switch-indicator{
    transform: translateX(calc(100% - 2px));
  }
  .switch-body.on{
  	box-shadow: 0 0 0 2px var(--primary);
  }
  .switch-body.on .switch-indicator{
  	background: var(--primary);
    transform: translateX(100%);
  }
  .rotunda{
  animation: rotateRot 120s linear infinite;
  }
    .rotunda.two{
  animation: rotateRot 90s linear reverse infinite;
  }
      .rotunda.three{
  animation: rotateRot 30s linear infinite;
  }
  @keyframes rotateRot {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(-360deg);
    }
}
.pulsate{
animation: pulsateShadow 5s ease-in-out infinite;}
@keyframes pulsateShadow {
    0%, 100% {
        box-shadow:0 0 0 0px var(--azure-blue),
        0 0 0 1px var(--azure-blue),  
        0 10px 50px 0 var(--bg); /* Initial and final shadow */
    }
    50% {
        box-shadow:0 0 0px 15px #10a3db7d, 
        0 0 0 1px var(--azure-blue), 
        0 10px 50px 0 var(--bg); /* Stronger shadow at the middle */
    }
}
[class*="pricing"] .w-nav-overlay, [class*="pricing"] nav {
    height: auto !important;
    background: unset !important;
    -webkit-backdrop-filter: unset !important;
    backdrop-filter:  unset !important;
}
[class*="pricing"] .w-nav-overlay{
    top: 2em !important;
}
</style>
          </div>
          <div class="script code-embed w-embed w-script">
            <script>
  for (const ele of document.querySelectorAll('.switch-body')) {
    ele.addEventListener("click", (e) => {
      if (e.target.classList.contains('on')) {
        e.target.classList.remove('on')
      } else {
        e.target.classList.add('on')
      }
    })
  }
  for (const billingSwitch of document.querySelectorAll('.billing-type')) {
    billingSwitch.addEventListener("click", (e) => {
      if (e.target.classList.contains('on')) {
        document.querySelectorAll(".monthly").forEach((ele) => { ele.style.display = "none" });
        document.querySelectorAll(".quarterly").forEach((ele) => { ele.style.display = "" });
      } else {
        document.querySelectorAll(".monthly").forEach((ele) => { ele.style.display = "" });
        document.querySelectorAll(".quarterly").forEach((ele) => { ele.style.display = "none" });
      }
    })
  }
  document.querySelectorAll(".monthly").forEach((ele) => { ele.style.display = "none" });
  document.querySelectorAll(".quarterly").forEach((ele) => { ele.style.display = "" });
</script>
            <script>
  function closeCurrencyMenu() {
    const currencyMenu = document.querySelector(".currency-menu");
    if (currencyMenu) {
      const navOverlay = currencyMenu.closest('[class*="nav-overlay"]');
      if (navOverlay) {
        // Hide the nav-overlay
        navOverlay.style.display = "none";
        // Find a sibling with [class*="nav-button"] and update attributes/classes
        const navButton = navOverlay.parentElement.querySelector('[class*="nav-button"]');
        if (navButton) {
          navButton.setAttribute("aria-expanded", "false");
          navButton.classList.remove("w--open");
        }
      }
    }
  }
  async function getForexData() {
    try {
      const response = await fetch('https://f18-pay-backend.vercel.app/api/v1/forex', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      if (data.result) {
        const gbp = data.result.find(item => item.name === "GBP");
        document.querySelectorAll(".price-exchange").forEach((ele) => {
          const raw = ele.innerHTML.replace(/[^0-9]/g, '');
          const btcValue = Number(raw) / Number(gbp.value);
          for (const currency of data.result) {
            const exchangedValue = Number(currency.value) * btcValue;
            let formattedValue;
            // Handle known currency codes
            try {
              let fraction = exchangedValue > 1000 ? 0 : 5;
              let workingValue = exchangedValue > 1000
                ? (Math.ceil(exchangedValue / 10) * 10) - 5
                : exchangedValue;
              const formatted = new Intl.NumberFormat(navigator.language, {
                style: 'currency',
                minimumFractionDigits: fraction,
                maximumFractionDigits: fraction,
                currency: currency.name
              }).formatToParts(workingValue);
              // Wrap the currency in a <span>
              formattedValue = formatted.map(part => {
                if (part.type === 'currency') {
                  return `<span>${part.value}</span>`;
                }
                return part.value;
              }).join('');
            } catch (error) {
              // Fallback for unsupported currencies like cryptocurrencies
              if (error instanceof RangeError) {
                formattedValue = exchangedValue.toLocaleString(navigator.language, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ` <span>${currency.name}</span>`;
              } else {
                throw error;
              }
            }
            ele.setAttribute(`data-value-${currency.name.toLowerCase()}`, formattedValue);
          }
          ele.setAttribute(`data-value-btc`, `${btcValue.toFixed(6)} BTC`);
        });
        // Populate the currency menu
        updateCurrencyMenu(data.result);
        // Automatically set the appropriate currency based on browser language
        const defaultCurrency = detectCurrencyFromLanguage(navigator.language, data.result);
        applyCurrency(defaultCurrency, data.result);
      }
    } catch (error) {
      console.error('Error fetching forex data:', error);
    }
  }
  // Detect appropriate currency based on browser language
  function detectCurrencyFromLanguage(language, availableCurrencies) {
    const currencyMapping = {
      'en-GB': 'GBP',
      'en-US': 'USD',
      'fr-FR': 'EUR',
      'de-DE': 'EUR',
      'es-ES': 'EUR',
      'ja-JP': 'JPY',
      'zh-CN': 'CNY'
      // Add more mappings as needed
    };
    // Get the mapped currency or default to USD
    const currencyCode = currencyMapping[language] || currencyMapping[language.split('-')[0]] || 'USD';
    // Ensure the detected currency is available, fallback to USD if not
    return availableCurrencies.some(currency => currency.name === currencyCode)
      ? currencyCode
      : 'USD';
  }
  // Apply the detected or selected currency to all price elements
  function applyCurrency(currencyCode, availableCurrencies) {
    document.querySelectorAll(".price-exchange").forEach((pricingEle) => {
      if (pricingEle.hasAttribute(`data-value-${currencyCode.toLowerCase()}`)) {
        pricingEle.innerHTML = pricingEle.getAttribute(`data-value-${currencyCode.toLowerCase()}`);
      }
    });
    // Highlight the selected currency in the menu
    document.querySelectorAll(".currency-link").forEach(link => {
      link.classList.toggle('active', link.innerHTML === currencyCode);
    });
    closeCurrencyMenu();
  }
  // Update the currency menu with available options
  function updateCurrencyMenu(availableCurrencies) {
    const currencyMenu = document.querySelector(".currency-menu");
    currencyMenu.innerHTML = ""; // Clear existing menu items
    availableCurrencies.push({ name: 'BTC' }); // Add BTC as an option
    for (const currency of availableCurrencies) {
      const currencyShortcut = document.createElement("a");
      currencyShortcut.innerHTML = currency.name;
      currencyShortcut.classList.add("currency-link");
      currencyShortcut.addEventListener("click", () => {
        applyCurrency(currency.name, availableCurrencies);
      });
      currencyMenu.appendChild(currencyShortcut);
    }
  }
  // Fetch forex data and initialise the UI
  getForexData();
</script>
          </div>
        </div>
      </section>
      <div data-w-id="7b971ea9-4abf-8f54-4186-163d25dead5d" class="content spread centre-vert content-build-in-target">
        <div id="faq" class="w-layout-vflex text-org">
          <h2 class="gradient-text ready">Commonly Asked Questions</h2>
          <div data-current="Why are slots limited?" data-easing="ease" data-duration-in="300" data-duration-out="100" class="tabs w-tabs">
            <div id="w-node-_7b971ea9-4abf-8f54-4186-163d25dead62-25dead5d" class="tabs-menu w-tab-menu">
              <a data-w-tab="Why are slots limited?" class="tab w-inline-block w-tab-link w--current">
                <div>What is Availability?</div>
                <div class="icon btn-icon w-embed">&#xF285;</div>
              </a>
              <a data-w-tab="How much does it cost" class="tab w-inline-block w-tab-link">
                <div>How much does it cost?</div>
                <div class="icon btn-icon w-embed">&#xF285;</div>
              </a>
              <a data-w-tab="Why sub. model" class="tab w-inline-block w-tab-link">
                <div>Why the subscription model?</div>
                <div class="icon btn-icon w-embed">&#xF285;</div>
              </a>
              <a data-w-tab="what diffwewnt timeframe" class="tab w-inline-block w-tab-link">
                <div>What if I need a different timeframe?</div>
                <div class="icon btn-icon w-embed">&#xF285;</div>
              </a>
              <a data-w-tab="how long to comp." class="tab w-inline-block w-tab-link">
                <div>How long will my project take to complete?</div>
                <div class="icon btn-icon w-embed">&#xF285;</div>
              </a>
              <a data-w-tab="teams?" class="tab w-inline-block w-tab-link">
                <div>Do you work with teams?</div>
                <div class="icon btn-icon w-embed">&#xF285;</div>
              </a>
              <a data-w-tab="who are the devs?" class="tab w-inline-block w-tab-link">
                <div>Who are the developers?</div>
                <div class="icon btn-icon w-embed">&#xF285;</div>
              </a>
            </div>
            <div id="w-node-_7b971ea9-4abf-8f54-4186-163d25dead7f-25dead5d" class="tabs-content w-tab-content">
              <div data-w-tab="Why are slots limited?" class="w-tab-pane w--tab-active">
                <div>
                  <div class="text-block-reading left-align">Development is a dynamic process, and things can take longer than expected.<br><br>We limit our availability to take on new projects so we can deliver revisions and progress updates for ongoing projects within 48 hours. <br><br>By limiting the number of active clients, we can prevent slow-downs and ensure everyone gets the attention they need.<br></div>
                </div>
              </div>
              <div data-w-tab="How much does it cost" class="w-tab-pane">
                <div>
                  <div class="text-block-reading left-align">We offer monthly slots at <span class="pricing-feature price-exchange">£2,995</span> each. <br><br>For longer projects or to save on future costs, we have a 3-month option for <span class="pricing-feature price-exchange">£7,485</span>, which comes out to <span class="pricing-feature price-exchange">£2,495</span> per month.<br>‍<br> With any subscription, you can queue up as many requests or jobs as you want, and we’ll tackle them in order. <br><br>You’ll get progress reports every 24-48 hours.<br>‍<br></div>
                  <a href="#pricing" class="btn link w-inline-block">
                    <div class="button-text">Pricing details</div>
                    <div class="icon btn-icon w-embed">&#xF145;</div>
                  </a>
                </div>
              </div>
              <div data-w-tab="Why sub. model" class="w-tab-pane">
                <div>
                  <div class="text-block-reading left-align">We’ve ditched per-hour billing for two big reasons. <br><br>First, different projects have their own ways of counting active hours, which is a headache for us and confusing for you.<br>‍<br>Second, per-hour billing feels too restrictive. With our subscription model, you can aim higher with your development goals. <br>Plus, by pre-paying, you stay in control of your budget and never have to worry about overspending.<br></div>
                </div>
              </div>
              <div data-w-tab="what diffwewnt timeframe" class="w-tab-pane">
                <div>
                  <div class="text-block-reading left-align">Absolutely! <br><br>If you need alternative timeframes for your development project, just let us know. <br><br>We understand that schedules can be tight or change unexpectedly, so we’ll do our best to accommodate your needs. <br>Whether you need to speed things up or take a bit more time, we’re flexible and can adjust our timelines to fit your situation. <br><br>Just reach out and we’ll figure it out together.<br></div>
                </div>
              </div>
              <div data-w-tab="how long to comp." class="w-tab-pane">
                <div>
                  <div class="text-block-reading left-align">We can give you an estimate once we get the details of your project. <br><br>We dislike overspending just as much as you do, so we keep our estimates upfront and realistic to make sure you always feel in control of your budget. <br><br>Plus, you can pause anytime and save up to half your unused time for later.<br></div>
                </div>
              </div>
              <div data-w-tab="teams?" class="w-tab-pane">
                <div>
                  <div class="text-block-reading left-align">We&#x27;re happy to join your team.<br><br>Just keep in mind that our availability and responsiveness might not always match up perfectly with your team’s schedule. <br><br>We’ll do our best to stay in sync and support your project.<br></div>
                </div>
              </div>
              <div data-w-tab="who are the devs?" class="w-tab-pane">
                <div>
                  <div class="text-block-reading left-align">Our team consists two permanent members; a Developer and a Designer. <br><br>Our Developer handles all the technical aspects, while our Designer focuses on making everything look great. <br><br>We’re a small but dedicated duo, dedicated to delivering high-quality work. <br><br>When you’re ready to meet us and discuss your project, just start a chat with us below. <br>We’re excited to collaborate with you!<br></div>
                </div>
              </div>
            </div>
          </div>
          <div class="w-layout-hflex btn-flex orientate-right">
            <a href="terms.html" class="btn link w-inline-block">
              <div class="button-text">Terms of Service</div>
              <div class="icon btn-icon w-embed">&#xF194;</div>
            </a>
            <a link="#chat" href="#" class="btn w-inline-block">
              <div class="button-background"></div>
              <div class="button-text">Chat with us</div>
              <div class="icon btn-icon w-embed">&#xF715;</div>
            </a>
          </div>
          <div class="w-embed">
            <style>
.tab[class*="current"] .icon{
	transform:rotate(90deg);
  transform-origin: center;
}
</style>
          </div>
        </div><img src="images/man-with-lightbumb.webp" loading="lazy" sizes="(max-width: 991px) 100vw, 36vw" srcset="images/man-with-lightbumb-p-500.webp 500w, images/man-with-lightbumb.webp 800w" alt="man with lightbulb
" class="hero-img content-img no-outline"><img src="images/blur3.webp" loading="lazy" sizes="(max-width: 767px) 98vw, (max-width: 888px) 90vw, 800px" srcset="images/blur3-p-500.webp 500w, images/blur3.webp 800w" alt="" class="blur right">
      </div>
      <div data-w-id="430ea16b-4232-c735-4857-66817cae92c7" class="content stats content-build-in-target">
        <div data-w-id="7de40a3e-a00f-c413-6bb4-c5c73de1930c" class="stats-wrapper-container">
          <div class="w-layout-hflex stats-wrapper">
            <div class="statistic-organiser no-border">
              <h3 class="statistics-label">Always<br>Learning</h3>
            </div>
            <div class="statistic-organiser">
              <h3 class="animated-counter _2">10</h3>
              <div class="stat-description">
                <h4 class="heading">Years</h4>
                <div class="stat-text">Building apps, services and financial solutions using Bitcoin</div>
              </div>
            </div>
            <div class="statistic-organiser">
              <div class="w-layout-hflex work-tile-text">
                <div class="w-layout-hflex flex-header-tile"><img loading="lazy" src="images/logo-download-small-square-128x128.png" alt="" class="work-tile-icon">
                  <h3 class="work-ile-title">Zettahash DAO</h3>
                </div>
                <div class="work-tile-text">Not only is this an industry-first for bitcoin mining decentralisation, but the technology stack is entirely serverless running on the latest, robust technologies.</div>
                <div class="technology-wrapper">
                  <div class="technologies-used">
                    <div class="technology webflow-key-colour zh"></div>
                    <div class="technology vue-key-colour zh"></div>
                  </div>
                  <ul role="list" class="work-list w-list-unstyled">
                    <li class="list-item">
                      <div class="technology-colour-key webflow-key-colour"></div>
                      <div class="technology-name">Webflow</div>
                      <div class="technology-pc">50%</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key vue-colour-key"></div>
                      <div class="technology-name">Vue3</div>
                      <div class="technology-pc">50%</div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="statistic-organiser">
              <h3 class="animated-counter _3">200</h3>
              <div class="stat-description">
                <h4 class="heading">Unique Designs</h4>
                <div class="stat-text">Supplied to delighted customers</div>
              </div>
            </div>
            <div class="statistic-organiser">
              <div class="w-layout-hflex work-tile-text">
                <div class="w-layout-hflex flex-header-tile"><img loading="lazy" src="images/btcpayserver-logo.webp" alt="btcpayserver logo
" class="work-tile-icon">
                  <h3 class="work-ile-title">BTCPay Server</h3>
                </div>
                <div class="work-tile-text">BTCPay Server is a self-hosted, open-source cryptocurrency payment processor that prioritises security, privacy, censorship resistance, and cost-effectiveness.</div>
                <div class="technology-wrapper">
                  <div class="technologies-used">
                    <div class="technology html5-tech-used"></div>
                    <div class="technology css-key-colour btc"></div>
                    <div class="technology js-key-colour btc"></div>
                  </div>
                  <ul role="list" class="work-list w-list-unstyled">
                    <li class="list-item">
                      <div class="technology-colour-key htmol5-colour-key"></div>
                      <div class="technology-name">HTML5</div>
                      <div class="technology-pc">30%</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key css-colour-key"></div>
                      <div class="technology-name">CSS3</div>
                      <div class="technology-pc">60%</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key js-colour-key"></div>
                      <div class="technology-name">JavaScript</div>
                      <div class="technology-pc">10%</div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="statistic-organiser no-border">
              <h3 class="statistics-label">We&#x27;re Natural<br>Problem-solvers</h3>
            </div>
            <div class="statistic-organiser">
              <h3 class="animated-counter _3">48</h3>
              <div class="stat-description">
                <h4 class="heading">Hours</h4>
                <div class="stat-text">Average delivery time for tasks and revisions.</div>
              </div>
            </div>
            <div class="statistic-organiser">
              <h3 class="animated-counter _3">80</h3>
              <div class="stat-description">
                <h4 class="heading">Thousand Commits</h4>
                <div class="stat-text">Encompassing years of contributions on GitHub and GitLab.</div>
              </div>
            </div>
          </div>
        </div>
        <div data-w-id="a05839e2-7015-18e8-722f-f81dd1b0c56c" class="stats-wrapper-container">
          <div class="w-layout-hflex stats-wrapper">
            <div class="statistic-organiser">
              <h3 class="animated-counter _2">30</h3>
              <div class="stat-description">
                <h4 class="heading">Websites &amp; Apps</h4>
                <div class="stat-text">Developed and deployed globally</div>
              </div>
            </div>
            <div class="statistic-organiser no-border">
              <h3 class="statistics-label">Current<br>Stats</h3>
            </div>
            <div class="statistic-organiser">
              <h3 class="animated-counter _3">7</h3>
              <div class="stat-description">
                <h4 class="heading">Years</h4>
                <div class="stat-text">Spent contributing to Free and Open Source projects and communities.</div>
              </div>
            </div>
            <div class="statistic-organiser">
              <div class="w-layout-hflex work-tile-text">
                <div class="w-layout-hflex flex-header-tile"><img loading="lazy" src="images/wallet-scrutiny-logo.webp" alt="walletscrutiny logo" class="work-tile-icon">
                  <h3 class="work-ile-title">WalletScrutiny</h3>
                </div>
                <div class="work-tile-text">WalletScrutiny helps everyday bitcoin users verify the legitimacy and security of their wallet by ensuring it is truly open-source.</div>
                <div class="technology-wrapper">
                  <div class="technologies-used">
                    <div class="technology js-key-colour"></div>
                    <div class="technology html5-tech-used ws"></div>
                    <div class="technology scss-key-colour ws"></div>
                  </div>
                  <ul role="list" class="work-list w-list-unstyled">
                    <li class="list-item">
                      <div class="technology-colour-key js-colour-key"></div>
                      <div class="technology-name">JavaScript</div>
                      <div class="technology-pc">90%</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key htmol5-colour-key"></div>
                      <div class="technology-name">HTML5</div>
                      <div class="technology-pc">5%</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key scss-colour-key"></div>
                      <div class="technology-name">SCSS</div>
                      <div class="technology-pc">5%</div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="statistic-organiser">
              <h3 class="animated-counter _2">20</h3>
              <div class="stat-description">
                <h4 class="heading">Open Source Projects</h4>
                <div class="stat-text">Contributed to, mastered, supported, integrated.</div>
              </div>
            </div>
            <div class="statistic-organiser no-border">
              <h3 class="statistics-label">Eliminating the guesswork</h3>
            </div>
            <div class="statistic-organiser">
              <h3 class="animated-counter _2">80</h3>
              <div class="stat-description">
                <h4 class="heading">Hours</h4>
                <div class="stat-text">Average development hours dedicated to your project, per calendar month.</div>
              </div>
            </div>
            <div class="statistic-organiser">
              <div class="w-layout-hflex work-tile-text">
                <div class="w-layout-hflex flex-header-tile"><img loading="lazy" src="images/flat18-logo.webp" alt="flat18 logo blue round" class="work-tile-icon">
                  <h3 class="work-ile-title">Development Ninjas</h3>
                </div>
                <div class="work-tile-text">Mastering a multitude of libraries, frameworks, and use cases, we’re your project’s perfect match.</div>
                <div class="technology-wrapper">
                  <div class="technologies-used">
                    <div class="technology html5-tech-used ee"></div>
                    <div class="technology scss-key-colour ee"></div>
                    <div class="technology js-key-colour ee"></div>
                    <div class="technology vue-key-colour ee"></div>
                    <div class="technology ns-key-colour ee"></div>
                    <div class="technology php-key-colour ee"></div>
                    <div class="technology node-key-colour ee"></div>
                    <div class="technology sql-key-colour ee"></div>
                    <div class="technology btc-key-colour ee"></div>
                    <div class="technology webflow-key-colour ee"></div>
                  </div>
                  <ul role="list" class="work-list w-list-unstyled">
                    <li class="list-item">
                      <div class="technology-colour-key htmol5-colour-key"></div>
                      <div class="technology-name">HTML5</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key scss-colour-key"></div>
                      <div class="technology-name">SCSS</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key js-colour-key"></div>
                      <div class="technology-name">JavaScript</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key vue-colour-key"></div>
                      <div class="technology-name">Vue3</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key ns-colour-key"></div>
                      <div class="technology-name">NativeScript</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key php-colour-key"></div>
                      <div class="technology-name">PHP</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key node-key-colour"></div>
                      <div class="technology-name">Node.js</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key sql-key-colour"></div>
                      <div class="technology-name">SQL</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key btc-key-colour"></div>
                      <div class="technology-name">Bitcoin Core</div>
                    </li>
                    <li class="list-item">
                      <div class="technology-colour-key _wf-key-colour"></div>
                      <div class="technology-name">Webflow</div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="statistic-organiser no-border">
              <h3 class="statistics-label">Development. Simplified.</h3>
            </div>
          </div>
        </div>
        <div class="code-embed-2 w-embed">
          <style>
.stats-wrapper-container > .stats-wrapper{animation: scroll-stats-wrapper  120s linear infinite;}
.stats-wrapper-container:nth-of-type(2) > .stats-wrapper{animation-direction: reverse;}
@keyframes scroll-stats-wrapper {
  0% {
    transform: translateX(calc(-25% - 60px));
  }
  100% {
    transform: translateX(calc(-75% - 60px));
  }
}
</style>
        </div>
        <div class="code-embed-3 w-embed w-script">
          <script>
    // Select the stats wrapper
    const statsWrappers = document.querySelectorAll('.stats-wrapper');
for (const statsWrapper of statsWrappers){
    // Get all elements with the class '.statistic-organiser' inside the stats wrapper
    const statisticOrganisers = statsWrapper.querySelectorAll('.statistic-organiser');
    // Number of times to duplicate the elements
    const duplicationCount = 3;
    // Loop to duplicate the elements
    for (let i = 0; i < duplicationCount; i++) {
        statisticOrganisers.forEach(function(organiser) {
            const clone = organiser.cloneNode(true); // Clone the element including its children
            statsWrapper.appendChild(clone); // Append the cloned element to the stats wrapper
        });
    }
    }
</script>
        </div>
      </div>
      <div id="wordsExperimentsSliderContainerWrapper" class="w-layout-vflex content content-build-in-target">
        <h2 class="gradient-text ready">Work &amp; Samples</h2>
        <div class="text-block-reading">Check out a few projects we&#x27;ve worked on.</div>
        <div id="wordsExperimentsSliderContainer" class="content works-and-exp-show-static">
          <div class="work-tile compact"><img src="images/wallet-scrut.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/wallet-scrut-p-500.webp 500w, images/wallet-scrut.webp 660w" alt="walletscrutiny website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">WalletScrutiny</h3>
              <div class="work-tile-text">Collaborative project with the Bitcoin Design Community to redesign the WalletScrutiny brand and website.<br></div>
              <a href="https://walletscrutiny.com" target="_blank" class="link w-inline-block">
                <div>Visit Website</div>
                <div class="icon small w-embed">&#xF1C5;</div>
              </a>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology js-key-colour ws"></div>
                  <div class="technology html5-tech-used ws"></div>
                  <div class="technology scss-key-colour ws"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">90%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key htmol5-colour-key"></div>
                    <div class="technology-name">HTML5</div>
                    <div class="technology-pc">5%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key scss-colour-key"></div>
                    <div class="technology-name">SCSS</div>
                    <div class="technology-pc">5%</div>
                  </li>
                </ul>
                <div class="framework-credit-wrapper"><img src="images/Jekyll_software_Logo.png" loading="lazy" alt="jekyll logo" height="40" class="framework-logo"></div>
              </div>
            </div>
            <div class="badge subtle">Current version</div>
          </div>
          <div class="work-tile compact"><img src="images/btcpay.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/btcpay-p-500.webp 500w, images/btcpay.webp 660w" alt="keevo website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">BTCPay Server</h3>
              <div class="work-tile-text">Clean, modern design for the BTCPay Server Main Landing page and Foundation Website.</div>
              <a href="https://btcpayserver.org" target="_blank" class="link w-inline-block">
                <div>Visit Website</div>
                <div class="icon small w-embed">&#xF1C5;</div>
              </a>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology html5-tech-used btcpay-server"></div>
                  <div class="technology css-key-colour btc"></div>
                  <div class="technology js-key-colour btc"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key htmol5-colour-key"></div>
                    <div class="technology-name">HTML5</div>
                    <div class="technology-pc">30%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key css-colour-key"></div>
                    <div class="technology-name">CSS</div>
                    <div class="technology-pc">60%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">10%</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="badge subtle">Current version</div>
          </div>
          <div class="work-tile compact"><img src="images/f18-pay-224.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/f18-pay-224-p-500.webp 500w, images/f18-pay-224.webp 660w" alt="keevo website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">F18 Pay</h3>
              <div class="work-tile-text">Bitcoin, ETH and ERC-20 token payments processor built to run in serverless environments.<br></div>
              <a href="https://pay.flat18.co.uk" target="_blank" class="link w-inline-block">
                <div>Visit Website</div>
                <div class="icon small w-embed">&#xF1C5;</div>
              </a>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology vue-key-colour f18"></div>
                  <div class="technology css-key-colour f18pay"></div>
                  <div class="technology js-key-colour f18-pay"></div>
                  <div class="technology html5-tech-used f18-pay"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key vue-colour-key"></div>
                    <div class="technology-name">Vue</div>
                    <div class="technology-pc">30%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key css-colour-key"></div>
                    <div class="technology-name">CSS</div>
                    <div class="technology-pc">10%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">50%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key htmol5-colour-key"></div>
                    <div class="technology-name">HTML5</div>
                    <div class="technology-pc">10%</div>
                  </li>
                </ul>
                <div class="framework-credit-wrapper"><img src="images/Vue.js_Logo_2.svg.png" loading="lazy" sizes="100vw" height="Auto" alt="vue logo" srcset="images/Vue.js_Logo_2.svg-p-500.png 500w, images/Vue.js_Logo_2.svg-p-800.png 800w, images/Vue.js_Logo_2.svg-p-1080.png 1080w, images/Vue.js_Logo_2.svg-p-1600.png 1600w, images/Vue.js_Logo_2.svg-p-2000.png 2000w, images/Vue.js_Logo_2.svg.png 2367w" class="framework-logo"></div>
              </div>
            </div>
            <div class="badge subtle">PUBLIC BETA</div>
          </div>
          <div class="work-tile compact"><img src="images/creative-studies23.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/creative-studies23-p-500.webp 500w, images/creative-studies23.webp 660w" alt="keevo website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact"># Hashboard</h3>
              <div class="work-tile-text">Web3 application enabling transparent operation and governance of the Zettahash DAO project.<br></div>
              <a href="https://hashboard.zettahash.org" target="_blank" class="link w-inline-block">
                <div>Visit Website</div>
                <div class="icon small w-embed">&#xF1C5;</div>
              </a>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology vue-key-colour f18"></div>
                  <div class="technology css-key-colour f18pay"></div>
                  <div class="technology js-key-colour f18-pay"></div>
                  <div class="technology html5-tech-used f18-pay"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key vue-colour-key"></div>
                    <div class="technology-name">Vue</div>
                    <div class="technology-pc">30%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key css-colour-key"></div>
                    <div class="technology-name">CSS</div>
                    <div class="technology-pc">10%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">50%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key htmol5-colour-key"></div>
                    <div class="technology-name">HTML5</div>
                    <div class="technology-pc">10%</div>
                  </li>
                </ul>
                <div class="framework-credit-wrapper"><img src="images/Vue.js_Logo_2.svg.png" loading="lazy" sizes="100vw" height="Auto" alt="vue logo" srcset="images/Vue.js_Logo_2.svg-p-500.png 500w, images/Vue.js_Logo_2.svg-p-800.png 800w, images/Vue.js_Logo_2.svg-p-1080.png 1080w, images/Vue.js_Logo_2.svg-p-1600.png 1600w, images/Vue.js_Logo_2.svg-p-2000.png 2000w, images/Vue.js_Logo_2.svg.png 2367w" class="framework-logo"></div>
              </div>
            </div>
            <div class="badge subtle">PUBLIC BETA</div>
          </div>
          <div class="work-tile compact"><img src="images/zettahash-2024.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/zettahash-2024-p-500.webp 500w, images/zettahash-2024.webp 660w" alt="keevo website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">Zettahash DAO</h3>
              <div class="work-tile-text">Zettahash website built in Webflow and designed to be processed in Node within a GitHub Pages environment.<br></div>
              <a href="https://zettahash-static.webflow.io" target="_blank" class="link w-inline-block">
                <div>Visit Website</div>
                <div class="icon small w-embed">&#xF1C5;</div>
              </a>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology webflow-key-colour _100"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key webflow-key-colour"></div>
                    <div class="technology-name">Webflow</div>
                    <div class="technology-pc">100%</div>
                  </li>
                </ul>
                <div class="framework-credit-wrapper"><img src="images/webflow-icon-4095338614.png" loading="lazy" alt="webflow logo
" height="Auto" class="framework-logo"></div>
              </div>
            </div>
            <div class="badge subtle">Live</div>
          </div>
          <div class="work-tile compact"><img src="images/creative-studies2.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/creative-studies2-p-500.webp 500w, images/creative-studies2.webp 660w" alt="keevo website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">NFT Project<br>BeraVote Family</h3>
              <div class="work-tile-text">A concept sample for a BeraVote NFT project built in Webflow for maximum content manageability.<br>We coupled this with extensive custom JS to enable serverless database management of NFT assets for the site gallery. </div>
              <a href="https://beravote-nft.pages.dev/" target="_blank" class="link w-inline-block">
                <div>Visit Website</div>
                <div class="icon small w-embed">&#xF1C5;</div>
              </a>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology webflow-key-colour _50"></div>
                  <div class="technology js-key-colour bv"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key webflow-key-colour"></div>
                    <div class="technology-name">Webflow SiteBuilder</div>
                    <div class="technology-pc">50%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">50%</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="badge subtle">demo</div>
          </div>
          <div class="work-tile compact"><img src="images/keevo-tile.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/keevo-tile-p-500.webp 500w, images/keevo-tile.webp 800w" alt="keevo website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">Keevo Wallet</h3>
              <div class="work-tile-text">Redesign of Keevo Wallet website to achieve a look which can be described as &quot;glossy&quot;, &quot;high-quality&quot; but also clean, modern and balancing &quot;sophisticated&quot; with &quot;trendy&quot;.</div>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology html5-tech-used keevo"></div>
                  <div class="technology scss-key-colour keevo"></div>
                  <div class="technology js-key-colour btc"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key htmol5-colour-key"></div>
                    <div class="technology-name">HTML5</div>
                    <div class="technology-pc">30%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key scss-colour-key"></div>
                    <div class="technology-name">SCSS</div>
                    <div class="technology-pc">60%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">10%</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="badge subtle">demo</div>
          </div>
          <div class="work-tile compact"><img src="images/incognetio-tile.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/incognetio-tile-p-500.webp 500w, images/incognetio-tile.webp 800w" alt="incognet website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">Incognet</h3>
              <div class="work-tile-text">Refreshing the Incognet website with improved UX and a cleaner, more-appealing UI and graphics.</div>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology php-key-colour incognet"></div>
                  <div class="technology scss-key-colour incognet"></div>
                  <div class="technology js-key-colour btc"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key php-colour-key"></div>
                    <div class="technology-name">PHP</div>
                    <div class="technology-pc">80%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key scss-colour-key"></div>
                    <div class="technology-name">SCSS</div>
                    <div class="technology-pc">10%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">10%</div>
                  </li>
                </ul>
                <div class="framework-credit-wrapper"><img src="images/php-logo.png" loading="lazy" sizes="100vw" height="Auto" alt="php logo" srcset="images/php-logo-p-500.png 500w, images/php-logo-p-800.png 800w, images/php-logo-p-1080.png 1080w, images/php-logo-p-1600.png 1600w, images/php-logo-p-2000.png 2000w, images/php-logo.png 2560w" class="framework-logo"></div>
              </div>
            </div>
            <div class="badge subtle">demo</div>
          </div>
          <div class="work-tile compact"><img src="images/zismo-tile.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/zismo-tile-p-500.webp 500w, images/zismo-tile.webp 800w" alt="zismo.io website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">Zismo.io</h3>
              <div class="work-tile-text">A Peer-to-Peer bitcoin trading platform which also encourages face-to-face trading</div>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology php-key-colour zismo"></div>
                  <div class="technology scss-key-colour zismo"></div>
                  <div class="technology js-key-colour zismo"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key php-colour-key"></div>
                    <div class="technology-name">PHP</div>
                    <div class="technology-pc">65%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key scss-colour-key"></div>
                    <div class="technology-name">SCSS</div>
                    <div class="technology-pc">15%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">20%</div>
                  </li>
                </ul>
                <div class="framework-credit-wrapper"><img src="images/php-logo.png" loading="lazy" sizes="100vw" height="Auto" alt="php logo" srcset="images/php-logo-p-500.png 500w, images/php-logo-p-800.png 800w, images/php-logo-p-1080.png 1080w, images/php-logo-p-1600.png 1600w, images/php-logo-p-2000.png 2000w, images/php-logo.png 2560w" class="framework-logo"></div>
              </div>
            </div>
            <div class="badge subtle">alpha</div>
          </div>
          <div class="work-tile compact"><img src="images/nairaex-tile.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/nairaex-tile-p-500.webp 500w, images/nairaex-tile.webp 800w" alt="naira website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">Naira Ex</h3>
              <div class="work-tile-text">Landing-page for a crypto debit card aimed at the Canadian and Nigerian market.</div>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology html5-tech-used"></div>
                  <div class="technology css-key-colour naira"></div>
                  <div class="technology js-key-colour naira"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key htmol5-colour-key"></div>
                    <div class="technology-name">HTML5</div>
                    <div class="technology-pc">30%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key css-colour-key"></div>
                    <div class="technology-name">CSS3</div>
                    <div class="technology-pc">30%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">40%</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="badge subtle">released</div>
          </div>
          <div class="work-tile compact"><img src="images/p2pnft-tile.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/p2pnft-tile-p-500.webp 500w, images/p2pnft-tile.webp 800w" alt="p2pnft website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">P2P NFT</h3>
              <div class="work-tile-text">Peer-to-peer NFT trading platform</div>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology vue-key-colour p2p"></div>
                  <div class="technology scss-key-colour p2p"></div>
                  <div class="technology js-key-colour btc"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key vue-colour-key"></div>
                    <div class="technology-name">Vue</div>
                    <div class="technology-pc">80%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key scss-colour-key"></div>
                    <div class="technology-name">SCSS</div>
                    <div class="technology-pc">10%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">10%</div>
                  </li>
                </ul>
                <div class="framework-credit-wrapper"><img src="images/Vue.js_Logo_2.svg.png" loading="lazy" sizes="100vw" height="Auto" alt="vue logo" srcset="images/Vue.js_Logo_2.svg-p-500.png 500w, images/Vue.js_Logo_2.svg-p-800.png 800w, images/Vue.js_Logo_2.svg-p-1080.png 1080w, images/Vue.js_Logo_2.svg-p-1600.png 1600w, images/Vue.js_Logo_2.svg-p-2000.png 2000w, images/Vue.js_Logo_2.svg.png 2367w" class="framework-logo"></div>
              </div>
            </div>
            <div class="badge subtle">released</div>
          </div>
          <div class="work-tile compact"><img src="images/walletscrutiny-tile.webp" loading="lazy" sizes="(max-width: 479px) 60vw, (max-width: 767px) 15vw, (max-width: 991px) 13vw, 15vw" srcset="images/walletscrutiny-tile-p-500.webp 500w, images/walletscrutiny-tile.webp 800w" alt="walletscrutiny website graphic" class="work-tile-image compact">
            <div class="w-layout-hflex work-tile-text">
              <h3 class="work-ile-title compact">WalletScrutiny</h3>
              <div class="work-tile-text">Earlier iterations of WalletScrutiny and experimental layouts, graphics, UX.</div>
              <div class="technology-wrapper">
                <div class="technologies-used">
                  <div class="technology js-key-colour ws"></div>
                  <div class="technology html5-tech-used ws"></div>
                  <div class="technology scss-key-colour ws"></div>
                </div>
                <ul role="list" class="work-list w-list-unstyled">
                  <li class="list-item">
                    <div class="technology-colour-key js-colour-key"></div>
                    <div class="technology-name">JavaScript</div>
                    <div class="technology-pc">90%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key htmol5-colour-key"></div>
                    <div class="technology-name">HTML5</div>
                    <div class="technology-pc">5%</div>
                  </li>
                  <li class="list-item">
                    <div class="technology-colour-key scss-colour-key"></div>
                    <div class="technology-name">SCSS</div>
                    <div class="technology-pc">5%</div>
                  </li>
                </ul>
                <div class="framework-credit-wrapper"><img src="images/Jekyll_software_Logo.png" loading="lazy" alt="jekyll logo" height="40" class="framework-logo"></div>
              </div>
            </div>
            <div class="badge subtle">earlier version</div>
          </div>
        </div>
      </div>
      <div data-w-id="47b29c46-e8cb-b814-fbc2-c2408e094676" class="content lets-build">
        <div class="flex-for-span-syle-diff">
          <div data-w-id="44fd184c-1bb5-9ef3-f9ed-13c793d9c3c7" class="image-9 _1">Let&#x27;s Get Creative Together</div>
          <div class="image-9 _2">Let&#x27;s Get Creative Together</div>
          <div class="image-9 _3">Let&#x27;s Get Creative Together</div>
          <aside link="#chat" class="btn before-footer-final-chat-cta link">
            <div class="button-text">Say Hello</div>
            <div class="icon btn-icon w-embed">&#xF715;</div>
          </aside>
        </div><img src="images/blur3.webp" loading="lazy" sizes="100vw" srcset="images/blur3-p-500.webp 500w, images/blur3.webp 800w" alt="" class="blur centre-alternate-colour">
      </div>
      <section class="footer-dark">
        <div class="content">
          <div class="footer-wrapper">
            <a href="#" class="footer-brand w-inline-block"><img src="images/logo-24-blue.svg" loading="lazy" alt="" height="30" class="image-4"></a>
            <div class="footer-content">
              <div id="w-node-e28b2c83-3d20-f0ea-2b37-5ec141346fa7-41346f98" class="footer-block">
                <div class="title-small">Contact</div>
                <a link="#chat" href="#" class="footer-link w-inline-block">
                  <div class="icon small x-small w-embed">&#xF24B;</div>
                  <div>Live Chat</div>
                </a>
                <a href="https://t.me/flat18_bot" class="footer-link w-inline-block">
                  <div class="icon small x-small w-embed">&#xF5B3;</div>
                  <div>Telegram</div>
                </a>
                <a href="mailto:<EMAIL>" class="footer-link w-inline-block">
                  <div class="icon small x-small w-embed">&#xF73B;</div>
                  <div><EMAIL></div>
                </a>
                <a href="https://x.com/f18_dev" class="footer-link w-inline-block">
                  <div class="icon small x-small w-embed">&#xF5EF;</div>
                  <div>Twitter</div>
                </a>
              </div>
              <div id="w-node-e28b2c83-3d20-f0ea-2b37-5ec141346f9e-41346f98" class="footer-block">
                <div class="title-small">Resources</div>
                <a href="https://stats.uptimerobot.com/RKBX3irMJl" class="footer-link w-inline-block">
                  <div class="status good"></div>
                  <div>Service status<br></div>
                </a>
                <a href="about.html" class="footer-link">About us</a>
                <a href="ease-of-communication-standard.html" class="footer-link">Standards</a>
                <a href="privacy.html" class="footer-link">Privacy Policy</a>
                <a href="donate-to-flat18.html" class="footer-link">Donations</a>
                <a href="free-services.html" class="footer-link">Free Services</a>
              </div>
              <div id="w-node-_26beecda-c968-eb58-1bc5-790c38ae2b87-41346f98" class="footer-block">
                <div class="title-small">Shortcuts</div>
                <a href="pricing.html" class="footer-link">Pricing</a>
                <a href="terms.html" class="footer-link">Terms of Service</a>
                <a href="https://accounts.flat18.co.uk/client/login" class="footer-link">Login</a>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-copyright-center">© 2012-<span current-year="true">20</span> FLAT 18</div>
        <div class="code w-embed w-script">
          <script>
for(const year of document.querySelectorAll(`[current-year="true"]`)){
year.innerHTML = new Date().getFullYear()
}
</script>
          <style>
.status.good{
 animation: pulseRing 3s linear forwards infinite;
}
@keyframes pulseRing{
0%{
	box-shadow:0px 0px 0px 0px #00785c;
}
100%{
	box-shadow:0px 0px 0px 5px #00785c00;
}
}
</style>
        </div>
        <div class="w-layout-hflex cookie-notice">
          <div id="w-node-_6c147dfb-07db-e23e-9b2b-165178d9814e-41346f98" class="w-layout-vflex flex-block-2">
            <div class="accept-cookies large-only">This website uses cookies to enhance your browsing experience. Click here to dismiss this notice and accept cookies.<br></div>
            <div class="accept-cookies small-only">We use cookies to enhance your experience.<br>Tap here to accept and close.</div>
            <a href="privacy.html" class="link cookie">Learn more in our Privacy Policy</a>
          </div>
          <div id="w-node-_6ec3e2e3-cf69-9240-e231-fc58c0b85d27-41346f98" class="icon cookie accept-cookies w-embed">&#xF659;</div>
          <div class="code w-embed w-script">
            <script>
  const userAcknowledgedCookie = localStorage.getItem("userAcceptedCookies") ? localStorage.getItem("userAcceptedCookies") : 'false'
  localStorage.setItem("userAcceptedCookies", userAcknowledgedCookie)
  const bodyClassUserCookies = userAcknowledgedCookie !== 'false' ? 'cookies-accepted':'cookies-not-accepted'
  document.body.classList.add(bodyClassUserCookies)
  document.querySelectorAll(".accept-cookies").forEach((e)=>{
    e.addEventListener("click", ()=>{
      localStorage.setItem("userAcceptedCookies", 'true')
      document.body.classList.add('cookies-accepted')
    })
  })
</script>
            <style>
.cookies-not-accepted .cookie-notice{display: flex;}
.cookies-accepted .cookie-notice{display: none;}
</style>
          </div>
        </div>
      </section>
    </div>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=663245c7d597883474e88492" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script>
  let theme = localStorage && localStorage.getItem('theme') ? (localStorage.getItem('theme') === 'dark' ? 'auto' : 'light') : 'auto'
  let size = window.outerWidth >= 756 ? "expanded_bubble" : "standard"
  window.chatwootSettings = {
    position: "right", type: size, launcherTitle: "Start here",
    darkMode: theme,
  };
  function initCW(d, t) {
    var BASE_URL = "https://chatwoot.flat18.co.uk";//;"https://app.chatwoot.com"
    var g = d.createElement(t), s = d.getElementsByTagName(t)[0];
    g.src = BASE_URL + "/packs/js/sdk.js";
    g.defer = true;
    g.async = true;
    g.classList.add("chatwoot-script-element")
    s.parentNode.insertBefore(g, s);
    g.onload = function () {
      window.chatwootSDK.run({
        websiteToken: 'krt1otbtLdpkie19rPwPThai',//'jvPpSh5d5zxrQDnanqRYtRx9',
        baseUrl: BASE_URL
      })
    }
  }
  initCW(document, "script")
  function makeid(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() *
        charactersLength));
    }
    return result;
  }
  window.addEventListener("chatwoot:ready", function () {
    // Use window.$chatwoot here
    // ...
    // let random = Date.now() + makeid(8)
    // window.$chatwoot.setUser(random, {
    //   //email: "<<EMAIL>>",
    //   //name: "<name-of-the-user>",
    //   avatar_url: "/src/img/avi.svg",
    //   //phone_number: "<phone-number-of-the-user>",
    // });
    let webMLocal = localStorage && localStorage.getItem("webM") ? localStorage.getItem("webM") : data.webM
    window.$chatwoot.setUser(webMLocal, { name: `${window.geoCityCountry} - ${webMLocal}` });
  });
  function observeParentOpacityChange(targetElement, callback) {
    // Get the grandparent element (parent > parent > parent)
    const grandparentElement = targetElement.parentElement?.parentElement?.parentElement;
    if (!grandparentElement) {
      console.warn('Grandparent element not found.');
      return;
    }
    // Create a callback function to execute when the opacity of the grandparent changes to 1
    function onOpacityChange(mutationsList, observer) {
      for (let mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const currentOpacity = window.getComputedStyle(grandparentElement).opacity;
          if (currentOpacity === '1') {
            // Trigger the provided callback function
            callback();
            // Disconnect the observer
            observer.disconnect();
            console.log('Observer disconnected.');
          }
        }
      }
    }
    // Create a MutationObserver instance and pass in the callback function
    const observer = new MutationObserver(onOpacityChange);
    // Start observing the grandparent element for attribute changes
    observer.observe(grandparentElement, { attributes: true });
  }
  // Original loop, modified to use the observer
  for (const ele of document.querySelectorAll(`[class*="animated-counter"]`)) {
    // Wrap your original setTimeout logic in a function
    const startAnimation = () => {
      let eleCount = 0;
      setTimeout(() => {
        let numb = Number(ele.innerHTML);
        let newNumb = 0;
        setInterval(() => {
          if (newNumb <= numb) {
            ele.innerHTML = newNumb;
            newNumb++;
          }
        }, 1500 / numb);
      }, 200 * eleCount);
      eleCount++;
    };
    // Use the observer to trigger the animation when the grandparent's opacity becomes 1
    observeParentOpacityChange(ele, startAnimation);
  }
  for (const chat of document.querySelectorAll(`[link*="chat"]`)) {
    chat.addEventListener("click", () => {
      window.$chatwoot.toggle("open")
      //Twitter Conversion Event
      twq('event', 'tw-oopi3-ooy6e', {
      });
    })
  }
</script>
</body>
</html>