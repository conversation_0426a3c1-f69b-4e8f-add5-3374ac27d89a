<!DOCTYPE html><!--  Last Published: Sun Dec 22 2024 21:22:50 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="668bf4cf87f079006a5b24f2" data-wf-site="663245c7d597883474e88492">
<head>
  <meta charset="utf-8">
  <title>Not Found</title>
  <meta content="Not Found" property="og:title">
  <meta content="Not Found" property="twitter:title">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/flat18.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <script type="text/javascript">!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.agent='plwebflow';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,document,'script','https://connect.facebook.net/en_US/fbevents.js');fbq('init', '745776286716427');fbq('track', 'PageView');</script>
  <style>
  [class*="btn"]:hover > [class*="button-background"]{
  	transform: translateX(50%);
  }
    [class*="btn"]:hover > [class*="icon"][class*="right"]{
  	transform: translateX(5px);
  }
  .animated-counter::after {
    content: '+';
}
</style>
  <script>
    if(window.location.protocol != 'https:') {
      if(location.href.indexOf("808")<0)
      location.href = location.href.replace("http://", "https://")
    }
    const q = localStorage && localStorage.getItem("webM")? `&webM=${localStorage.getItem("webM")}` : ""
    fetch('https://api.flat18.co.uk/metrics/webm/index.php?geo=1' + q)
      .then(response => response.json())
      .then(data => {
        window.webM = data.webM
        window.geoCityCountry = data.geo
        let persist = localStorage && localStorage.getItem("webM")? localStorage.getItem("webM") : data.webM
        localStorage.setItem("webM", persist)
      });
  </script>
  <script defer="" src="https://eu.umami.is/script.js" data-website-id="54c1aa36-ac18-426d-ba14-3d5827cfa465"></script>
  <script async="" src="https://master--melodic-taffy-1a4c18.netlify.app/tracker.js" data-ackee-server="https://master--melodic-taffy-1a4c18.netlify.app" data-ackee-domain-id="b28e2698-bf04-4e23-9075-a5f7110affe0"></script>
  <!--  Twitter conversion tracking base code  -->
  <script>
!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
twq('config','oopi3');
</script>
  <!--  End Twitter conversion tracking base code  -->
</head>
<body>
  <div class="utility-page-wrap">
    <div class="utility-page-content"><img src="https://d3e54v103j8qbb.cloudfront.net/static/page-not-found.211a85e40c.svg" alt="">
      <h2>Page Not Found</h2>
      <div>The page you are looking for doesn&#x27;t exist or has been moved</div>
    </div>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=663245c7d597883474e88492" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script>
  let theme = localStorage && localStorage.getItem('theme') ? (localStorage.getItem('theme') === 'dark' ? 'auto' : 'light') : 'auto'
  let size = window.outerWidth >= 756 ? "expanded_bubble" : "standard"
  window.chatwootSettings = {
    position: "right", type: size, launcherTitle: "Start here",
    darkMode: theme,
  };
  function initCW(d, t) {
    var BASE_URL = "https://chatwoot.flat18.co.uk";//;"https://app.chatwoot.com"
    var g = d.createElement(t), s = d.getElementsByTagName(t)[0];
    g.src = BASE_URL + "/packs/js/sdk.js";
    g.defer = true;
    g.async = true;
    g.classList.add("chatwoot-script-element")
    s.parentNode.insertBefore(g, s);
    g.onload = function () {
      window.chatwootSDK.run({
        websiteToken: 'krt1otbtLdpkie19rPwPThai',//'jvPpSh5d5zxrQDnanqRYtRx9',
        baseUrl: BASE_URL
      })
    }
  }
  initCW(document, "script")
  function makeid(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() *
        charactersLength));
    }
    return result;
  }
  window.addEventListener("chatwoot:ready", function () {
    // Use window.$chatwoot here
    // ...
    // let random = Date.now() + makeid(8)
    // window.$chatwoot.setUser(random, {
    //   //email: "<<EMAIL>>",
    //   //name: "<name-of-the-user>",
    //   avatar_url: "/src/img/avi.svg",
    //   //phone_number: "<phone-number-of-the-user>",
    // });
    let webMLocal = localStorage && localStorage.getItem("webM") ? localStorage.getItem("webM") : data.webM
    window.$chatwoot.setUser(webMLocal, { name: `${window.geoCityCountry} - ${webMLocal}` });
  });
  function observeParentOpacityChange(targetElement, callback) {
    // Get the grandparent element (parent > parent > parent)
    const grandparentElement = targetElement.parentElement?.parentElement?.parentElement;
    if (!grandparentElement) {
      console.warn('Grandparent element not found.');
      return;
    }
    // Create a callback function to execute when the opacity of the grandparent changes to 1
    function onOpacityChange(mutationsList, observer) {
      for (let mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const currentOpacity = window.getComputedStyle(grandparentElement).opacity;
          if (currentOpacity === '1') {
            // Trigger the provided callback function
            callback();
            // Disconnect the observer
            observer.disconnect();
            console.log('Observer disconnected.');
          }
        }
      }
    }
    // Create a MutationObserver instance and pass in the callback function
    const observer = new MutationObserver(onOpacityChange);
    // Start observing the grandparent element for attribute changes
    observer.observe(grandparentElement, { attributes: true });
  }
  // Original loop, modified to use the observer
  for (const ele of document.querySelectorAll(`[class*="animated-counter"]`)) {
    // Wrap your original setTimeout logic in a function
    const startAnimation = () => {
      let eleCount = 0;
      setTimeout(() => {
        let numb = Number(ele.innerHTML);
        let newNumb = 0;
        setInterval(() => {
          if (newNumb <= numb) {
            ele.innerHTML = newNumb;
            newNumb++;
          }
        }, 1500 / numb);
      }, 200 * eleCount);
      eleCount++;
    };
    // Use the observer to trigger the animation when the grandparent's opacity becomes 1
    observeParentOpacityChange(ele, startAnimation);
  }
  for (const chat of document.querySelectorAll(`[link*="chat"]`)) {
    chat.addEventListener("click", () => {
      window.$chatwoot.toggle("open")
      //Twitter Conversion Event
      twq('event', 'tw-oopi3-ooy6e', {
      });
    })
  }
</script>
</body>
</html>