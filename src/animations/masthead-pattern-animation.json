{"v": "5.7.4", "fr": 60, "ip": 0, "op": 680, "w": 1202, "h": 1202, "nm": "Masthead Pattern Animation", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Background", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [1202, 1202]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.05, 0.05, 0.1, 1]}, "o": {"a": 0, "k": 100}}], "ip": 0, "op": 680, "st": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Trail Path 1", "sr": 1, "ks": {"o": {"a": 0, "k": 25}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-45, -30], [-80, 15], [-60, 45], [-20, 60], [30, 50], [70, 20], [85, -25], [45, -55], [10, -70], [-25, -65]], "o": [[45, 30], [80, -15], [60, -45], [20, -60], [-30, -50], [-70, -20], [-85, 25], [-45, 55], [-10, 70], [25, 65], [0, 0]], "v": [[-401, -401], [-320, -280], [-150, -200], [50, -120], [180, 20], [220, 180], [120, 320], [-80, 380], [-280, 280], [-380, 100], [-420, -150]], "c": true}}}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.6, 1, 0.3]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}}], "ip": 0, "op": 680, "st": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Floating Particle 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [90]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 650, "s": [90]}, {"t": 680, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 680, "s": [720]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [200, 200, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [281, 321, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 240, "s": [451, 401, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 360, "s": [671, 481, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 480, "s": [821, 621, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 600, "s": [721, 901, 0]}, {"t": 680, "s": [521, 981, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 340, "s": [140, 140, 100]}, {"t": 680, "s": [60, 60, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [35, 35]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.2, 0.6, 1, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "st", "c": {"a": 0, "k": [0.4, 0.8, 1, 0.6]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}}], "ip": 0, "op": 680, "st": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Trail Path 2", "sr": 1, "ks": {"o": {"a": 0, "k": 20}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [35, -40], [65, -10], [70, 35], [40, 65], [-20, 70], [-60, 45], [-80, 10], [-65, -30], [-30, -60], [15, -75]], "o": [[-35, 40], [-65, 10], [-70, -35], [-40, -65], [20, -70], [60, -45], [80, -10], [65, 30], [30, 60], [-15, 75], [0, 0]], "v": [[350, -250], [280, -150], [120, -80], [-20, 50], [-120, 200], [-180, 350], [-80, 420], [100, 380], [250, 250], [320, 80], [380, -120]], "c": true}}}, {"ty": "st", "c": {"a": 0, "k": [1, 0.4, 0.8, 0.4]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}}], "ip": 60, "op": 680, "st": 60}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Floating Particle 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [85]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 620, "s": [85]}, {"t": 650, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 680, "s": [-540]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.445, "y": 1}, "o": {"x": 0.555, "y": 0}, "t": 60, "s": [951, 351, 0]}, {"i": {"x": 0.445, "y": 1}, "o": {"x": 0.555, "y": 0}, "t": 180, "s": [881, 451, 0]}, {"i": {"x": 0.445, "y": 1}, "o": {"x": 0.555, "y": 0}, "t": 300, "s": [721, 551, 0]}, {"i": {"x": 0.445, "y": 1}, "o": {"x": 0.555, "y": 0}, "t": 420, "s": [481, 651, 0]}, {"i": {"x": 0.445, "y": 1}, "o": {"x": 0.555, "y": 0}, "t": 540, "s": [421, 951, 0]}, {"i": {"x": 0.445, "y": 1}, "o": {"x": 0.555, "y": 0}, "t": 600, "s": [521, 981, 0]}, {"t": 680, "s": [701, 981, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [70, 70, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 370, "s": [130, 130, 100]}, {"t": 680, "s": [70, 70, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [28, 28]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.4, 0.8, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "st", "c": {"a": 0, "k": [1, 0.6, 0.9, 0.7]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}}], "ip": 60, "op": 680, "st": 60}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Trail Path 3", "sr": 1, "ks": {"o": {"a": 0, "k": 18}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-25, -50], [-55, -25], [-70, 20], [-55, 50], [-15, 65], [25, 60], [55, 35], [70, 5], [60, -35], [25, -60]], "o": [[25, 50], [55, 25], [70, -20], [55, -50], [15, -65], [-25, -60], [-55, -35], [-70, -5], [-60, 35], [-25, 60], [0, 0]], "v": [[-150, -350], [-100, -220], [20, -150], [180, -120], [280, -20], [320, 120], [280, 280], [150, 350], [-20, 380], [-180, 320], [-280, 150]], "c": true}}}, {"ty": "st", "c": {"a": 0, "k": [0.4, 1, 0.6, 0.35]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}}], "ip": 180, "op": 680, "st": 180}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Geometric Shape 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 580, "s": [50]}, {"t": 610, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0]}, {"t": 680, "s": [360]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.75, "y": 0}, "t": 120, "s": [601, 601, 0]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.75, "y": 0}, "t": 280, "s": [751, 521, 0]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.75, "y": 0}, "t": 440, "s": [681, 721, 0]}, {"t": 600, "s": [521, 681, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 120, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 400, "s": [160, 160, 100]}, {"t": 680, "s": [80, 80, 100]}]}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [120, 120]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 25}}, {"ty": "st", "c": {"a": 0, "k": [0.8, 0.9, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}}], "ip": 120, "op": 680, "st": 120}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Floating Particle 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 210, "s": [80]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 600, "s": [80]}, {"t": 630, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [0]}, {"t": 680, "s": [630]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.215, "y": 1}, "o": {"x": 0.785, "y": 0}, "t": 180, "s": [451, 251, 0]}, {"i": {"x": 0.215, "y": 1}, "o": {"x": 0.785, "y": 0}, "t": 300, "s": [381, 371, 0]}, {"i": {"x": 0.215, "y": 1}, "o": {"x": 0.785, "y": 0}, "t": 420, "s": [601, 481, 0]}, {"i": {"x": 0.215, "y": 1}, "o": {"x": 0.785, "y": 0}, "t": 540, "s": [821, 481, 0]}, {"i": {"x": 0.215, "y": 1}, "o": {"x": 0.785, "y": 0}, "t": 600, "s": [881, 601, 0]}, {"t": 680, "s": [751, 751, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 180, "s": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 430, "s": [110, 110, 100]}, {"t": 680, "s": [50, 50, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [22, 22]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 1, 0.6, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "st", "c": {"a": 0, "k": [0.6, 1, 0.8, 0.8]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}}], "ip": 180, "op": 680, "st": 180}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Concentric Ring 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 340, "s": [45]}, {"t": 680, "s": [15]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 680, "s": [360]}]}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 340, "s": [140, 140, 100]}, {"t": 680, "s": [80, 80, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [320, 320]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "st", "c": {"a": 0, "k": [0.6, 0.8, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.5}}], "ip": 0, "op": 680, "st": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Concentric Ring 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 380, "s": [35]}, {"t": 680, "s": [12]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 680, "s": [-270]}]}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [110, 110, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 380, "s": [180, 180, 100]}, {"t": 680, "s": [110, 110, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [480, 480]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "st", "c": {"a": 0, "k": [0.4, 0.7, 0.9, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}}], "ip": 40, "op": 680, "st": 40}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Pulsing Core", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170, "s": [80]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 340, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 510, "s": [80]}, {"t": 680, "s": [30]}]}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 170, "s": [120, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 340, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 510, "s": [120, 120, 100]}, {"t": 680, "s": [80, 80, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [60, 60]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}}], "ip": 0, "op": 680, "st": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Asymmetric Orbit Ring", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [8]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 420, "s": [25]}, {"t": 680, "s": [8]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"t": 680, "s": [450]}]}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 80, "s": [140, 140, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 420, "s": [220, 220, 100]}, {"t": 680, "s": [140, 140, 100]}]}}, "ao": 0, "shapes": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-30, -40], [-50, -15], [-55, 25], [-35, 45], [0, 50], [35, 45], [55, 25], [50, -15], [30, -40]], "o": [[30, 40], [50, 15], [55, -25], [35, -45], [0, -50], [-35, -45], [-55, -25], [-50, 15], [-30, 40], [0, 0]], "v": [[-280, -200], [-220, -120], [-100, -80], [20, -60], [120, -20], [140, 60], [120, 140], [20, 180], [-100, 200], [-220, 240]], "c": true}}}, {"ty": "st", "c": {"a": 0, "k": [0.3, 0.6, 0.8, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}}], "ip": 80, "op": 680, "st": 80}], "markers": []}