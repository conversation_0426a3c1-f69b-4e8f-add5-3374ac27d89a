.button,.buttonContent{position:relative;align-items:center}.button{display:inline-flex;justify-content:center;padding:var(--space-3) var(--space-6);border-radius:12px;font-weight:var(--font-medium);font-size:var(--text-base);transition:all .25s cubic-bezier(.25,.46,.45,.94);cursor:pointer;border:0;overflow:hidden;text-decoration:none;letter-spacing:.02em;backdrop-filter:blur(5px);z-index:1}.button::after{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,rgba(255,255,255,0)0,rgba(255,255,255,.05) 25%,rgba(255,255,255,.2) 50%,rgba(255,255,255,.05) 75%,rgba(255,255,255,0) 100%);z-index:1;transition:left .7s ease;pointer-events:none}.button:hover::after{left:100%}.button::before{content:"";position:absolute;inset:0;z-index:-1;opacity:0;transition:opacity .3s ease;border-radius:inherit}.buttonContent{z-index:2;display:flex;gap:8px}.primary{background:linear-gradient(92deg,var(--primary) 0%,var(--primary-light) 100%);color:var(--bg-dark);box-shadow:0 4px 15px rgba(25,253,178,.2),inset 0 1px 0 rgba(255,255,255,.2);text-shadow:0 1px 1px rgba(0,0,0,.1)}.primary::before{background:linear-gradient(92deg,var(--primary-light) 0%,var(--primary) 100%);box-shadow:0 8px 25px rgba(25,253,178,.4)}.primary:hover{transform:translateY(-3px);box-shadow:0 10px 25px rgba(25,253,178,.3),inset 0 1px 0 rgba(255,255,255,.3)}.primary:hover::before{opacity:1}.primary:active{transform:translateY(0);box-shadow:0 5px 15px rgba(25,253,178,.2),inset 0 1px 0 rgba(255,255,255,.2);transition:all .1s ease}.text{background:0 0;color:var(--text-secondary);padding:var(--space-2);backdrop-filter:none}.text:hover{color:var(--primary);transform:translateY(-1px)}.text:active{transform:translateY(0)}.fullWidth{width:100%}.button i{font-size:1.2em;transition:transform .2s ease}.button:hover i{transform:translateX(3px)}@media (max-width:768px){.button{padding:var(--space-3) var(--space-5)}}@media (max-width:480px){.button{padding:var(--space-2) var(--space-4);font-size:var(--text-sm)}}