.testimonialsSection {
  padding: var(--section-spacing-y)0;
  position: relative;
  overflow: hidden;
  background-color: var(--bg-modern-dark)
}

.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  pointer-events: none
}

.backgroundGlow {
  position: absolute;
  top: -20%;
  left: -10%;
  width: 120%;
  height: 140%;
  background: radial-gradient(circle at 20% 30%, rgba(25, 253, 178, .06), transparent 40%), radial-gradient(circle at 80% 70%, rgba(157, 78, 255, .06), transparent 40%);
  filter: blur(120px);
  opacity: .8;
  z-index: 0
}

.backgroundGrid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to right, var(--section-bg-testimonials) 1px, transparent 1px), linear-gradient(to bottom, var(--section-bg-testimonials) 1px, transparent 1px);
  background-size: 40px 40px;
  opacity: .3;
  z-index: 0
}

.sectionHeader {
  text-align: center;
  margin-bottom: var(--space-16);
  position: relative;
  z-index: 1
}

.sectionTag {
  display: inline-block;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: .1em;
  color: var(--primary);
  background-color: rgba(25, 253, 178, .08);
  padding: .5rem 1rem;
  border-radius: 50px;
  margin-bottom: var(--space-4)
}

.sectionTitle {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-4);
  background: var(--primary-gradient);
  background-clip: text;
  color: transparent;
  letter-spacing: -.02em
}

.sectionSubtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--leading-relaxed)
}

.carouselContainer {
  position: relative;
  margin: var(--space-12)0;
  z-index: 1;
  overflow: hidden;
  border-radius: var(--radius-lg)
}

.carouselTrack {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x proximity;
  scrollbar-width: none;
  -ms-overflow-style: none;
  gap: var(--space-6);
  padding: var(--space-4)0 var(--space-8);
  margin: 0-1rem;
  scroll-padding: 0;
  position: relative;
  perspective: 1000px
}

.carouselTrack::before, .carouselTrack::after {
  content: "";
  flex: 0 0 calc(50% - 200px);
  min-width: 20px
}

.carouselOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 3;
  background: linear-gradient(to right, var(--section-bg-testimonials) 0%, transparent 15%, transparent 85%, var(--section-bg-testimonials) 100%)
}

.carouselTrack::-webkit-scrollbar {
  display: none
}

.testimonialCard {
  flex: 0 0 400px;
  scroll-snap-align: center;
  border-radius: var(--radius-xl);
  overflow: hidden;
  position: relative;
  cursor: grab;
  transform-origin: center center;
  margin: 0 calc(var(--space-4)/2);
  opacity: .65;
  filter: saturate(.8) blur(.5px);
  transform: scale(.95) translateZ(-10px)
}

.testimonialCard:active {
  cursor: grabbing
}

.cardInner {
  position: relative;
  background: var(--glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-md);
  transition: all .4s cubic-bezier(.22, 1, .36, 1)
}

.testimonialCard:hover .cardInner {
  box-shadow: var(--shadow-lg);
  border-color: var(--border-medium)
}

.activeCard {
  opacity: 1;
  filter: saturate(1) blur(0);
  transform: scale(1.02) translateZ(0);
  z-index: 2
}

.activeCard .cardInner {
  border-color: rgba(25, 253, 178, .3);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(25, 253, 178, .15)
}

.testimonialCard:nth-child(n+1):nth-child(-n+5) {
  opacity: .85;
  filter: saturate(.9) blur(0);
  transform: scale(.98) translateZ(-5px)
}

.primary.activeCard .cardInner {
  border-color: rgba(0, 236, 240, .3);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(0, 236, 240, .15)
}

.secondary.activeCard .cardInner {
  border-color: rgba(61, 158, 238, .3);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(61, 158, 238, .15)
}

.accent-purple.activeCard .cardInner {
  border-color: rgba(157, 78, 255, .3);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(157, 78, 255, .15)
}

.accent-teal.activeCard .cardInner {
  border-color: rgba(0, 215, 195, .3);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(0, 215, 195, .15)
}

.accent-pink.activeCard .cardInner {
  border-color: rgba(255, 78, 189, .3);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(255, 78, 189, .15)
}

.quoteMarkTop, .quoteMarkBottom {
  position: absolute;
  opacity: .15;
  transition: opacity .3s ease
}

.quoteMarkTop {
  top: var(--space-4);
  left: var(--space-4)
}

.quoteMarkBottom {
  bottom: var(--space-4);
  right: var(--space-4);
  transform: rotate(180deg)
}

.testimonialCard:hover .quoteMarkTop, .testimonialCard:hover .quoteMarkBottom {
  opacity: .25
}

.testimonialContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  z-index: 1
}

.ratingStars {
  display: flex;
  gap: var(--space-1);
  margin-bottom: var(--space-4)
}

.starFilled, .starEmpty {
  font-size: var(--text-base)
}

.starFilled {
  color: var(--accent-yellow)
}

.starEmpty {
  color: rgba(255, 255, 255, .2)
}

.testimonialText {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  flex-grow: 1;
  position: relative
}

.testimonialAuthor {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-top: auto;
  padding-top: var(--space-4);
  border-top: 1px solid rgba(255, 255, 255, .06)
}

.authorInitial {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  background: linear-gradient(135deg, rgba(25, 253, 178, .2), rgba(0, 236, 240, .2));
  box-shadow: 0 4px 10px rgba(0, 0, 0, .2)
}

.primary .authorInitial {
  background: linear-gradient(135deg, rgba(0, 236, 240, .2), rgba(0, 209, 202, .2))
}

.secondary .authorInitial {
  background: linear-gradient(135deg, rgba(61, 158, 238, .2), rgba(42, 127, 208, .2))
}

.accent-purple .authorInitial {
  background: linear-gradient(135deg, rgba(157, 78, 255, .2), rgba(255, 78, 189, .2))
}

.accent-teal .authorInitial {
  background: linear-gradient(135deg, rgba(25, 253, 178, .2), rgba(0, 215, 195, .2))
}

.accent-pink .authorInitial {
  background: linear-gradient(135deg, rgba(255, 78, 189, .2), rgba(255, 78, 110, .2))
}

.authorDetails {
  text-align: left
}

.authorName {
  font-size: var(--text-base);
  color: var(--text-primary);
  margin: 0;
  font-weight: var(--font-medium)
}

.authorRole {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin: 0
}

.carouselControls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
  margin-top: var(--space-6)
}

.carouselArrow {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: rgba(255, 255, 255, .05);
  border: 1px solid rgba(255, 255, 255, .1);
  color: var(--text-secondary);
  font-size: var(--text-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all .3s cubic-bezier(.22, 1, .36, 1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden
}

.carouselArrow:hover {
  background: rgba(255, 255, 255, .1);
  color: var(--text-primary);
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, .2)
}

.carouselArrow:active {
  transform: translateY(0)
}

.arrowPressed {
  transform: scale(.95) !important;
  background: rgba(25, 253, 178, .1) !important;
  border-color: rgba(25, 253, 178, .2) !important
}

.arrowDisabled {
  opacity: .5;
  cursor: default;
  pointer-events: none
}

.carouselArrow::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, .2)0, transparent 70%);
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: transform .5s ease, opacity .5s ease;
  border-radius: 50%
}

.carouselArrow:hover::after {
  transform: translate(-50%, -50%) scale(1.5);
  opacity: .5
}

.prevArrow:hover {
  transform: translateY(-2px) translateX(-2px)
}

.nextArrow:hover {
  transform: translateY(-2px) translateX(2px)
}

.carouselIndicators {
  display: flex;
  gap: var(--space-2);
  padding: var(--space-2)
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, .15);
  border: 0;
  padding: 0;
  cursor: pointer;
  transition: all .3s cubic-bezier(.22, 1, .36, 1);
  position: relative
}

.indicator:hover {
  background: rgba(255, 255, 255, .3);
  transform: scale(1.1)
}

.indicatorActive {
  background: var(--primary);
  transform: scale(1.2)
}

.indicatorPressed {
  transform: scale(.9) !important
}

.indicatorActive::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: var(--primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: .5;
  animation: indicatorPulse 2s infinite
}

@keyframes indicatorPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: .5
  }

  70% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0
  }

  to {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0
  }
}

.ctaWrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--space-12);
  position: relative;
  z-index: 1
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: .75rem;
  padding: .875rem 1.75rem;
  background: var(--primary-gradient);
  color: var(--bg-modern-dark);
  font-weight: var(--font-semibold);
  border-radius: var(--radius-md);
  transition: all .3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md)
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--glow-primary)
}

.ctaButton:active {
  transform: translateY(0)
}

@media (min-width:768px) {
  .carouselTrack {
    padding: var(--space-4)0 var(--space-8);
    margin: 0
  }

  .carouselTrack::before, .carouselTrack::after {
    flex: 0 0 calc(50% - 200px)
  }

  .testimonialCard {
    flex: 0 0 400px
  }
}

@media (min-width:1024px) {
  .carouselTrack {
    padding: var(--space-4)0 var(--space-8);
    margin: 0
  }

  .carouselTrack::before, .carouselTrack::after {
    flex: 0 0 calc(50% - 200px)
  }

  .testimonialCard {
    flex: 0 0 400px
  }
}

@media (max-width:767px) {
  .carouselTrack {
    padding: var(--space-4)0 var(--space-8)
  }

  .carouselTrack::before, .carouselTrack::after {
    flex: 0 0 calc(50% - 150px)
  }

  .testimonialCard {
    flex: 0 0 300px
  }
}

@media (max-width:768px) {
  .testimonialsSection {
    padding: var(--section-spacing-y-tablet)0
  }

  .sectionTitle {
    font-size: var(--text-3xl)
  }

  .sectionSubtitle {
    font-size: var(--text-base)
  }

  .testimonialText {
    font-size: var(--text-base)
  }

  .authorInitial {
    width: 40px;
    height: 40px;
    font-size: var(--text-base)
  }

  .carouselArrow {
    width: 36px;
    height: 36px;
    font-size: var(--text-base)
  }
}

@media (max-width:480px) {
  .testimonialsSection {
    padding: var(--section-spacing-y-mobile)0
  }

  .sectionTag {
    font-size: .65rem;
    padding: .4rem .8rem
  }

  .sectionTitle {
    font-size: var(--text-2xl)
  }

  .sectionSubtitle {
    font-size: var(--text-sm)
  }

  .cardInner {
    padding: var(--space-6)
  }

  .testimonialText {
    font-size: var(--text-sm);
    margin-bottom: var(--space-4)
  }

  .authorInitial {
    width: 36px;
    height: 36px;
    font-size: var(--text-sm)
  }

  .authorName {
    font-size: var(--text-sm)
  }

  .authorRole {
    font-size: var(--text-xs)
  }

  .carouselArrow {
    width: 32px;
    height: 32px;
    font-size: var(--text-sm)
  }

  .indicator {
    width: 6px;
    height: 6px
  }

  .ctaButton {
    padding: .75rem 1.5rem;
    font-size: var(--text-sm)
  }
}