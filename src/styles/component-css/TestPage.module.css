.testSection{padding:var(--space-16)0;position:relative;overflow:hidden;background-color:var(--bg-surface)}.container{max-width:var(--container-lg);margin:0 auto;padding:0 var(--space-4);position:relative;z-index:1}.heading{font-size:var(--text-3xl);font-weight:var(--font-bold);margin-bottom:var(--space-8);text-align:center}.cardsContainer{display:grid;grid-template-columns:repeat(3,1fr);gap:var(--space-6);margin-top:var(--space-8)}.card{background-color:rgba(255,255,255,.02);border-radius:var(--radius-lg);border:1px solid var(--border-light);padding:var(--space-8);transition:all var(--transition-normal);height:100%}.card:hover{transform:translateY(-8px);border-color:var(--border-medium);box-shadow:var(--shadow-lg)}.cardTitle{font-size:var(--text-xl);font-weight:var(--font-semibold);margin-bottom:var(--space-4);color:var(--text-primary)}.cardDescription{font-size:var(--text-base);color:var(--text-secondary);line-height:var(--leading-relaxed)}@media (max-width:768px){.cardsContainer{grid-template-columns:repeat(2,1fr)}}@media (max-width:480px){.cardsContainer{grid-template-columns:1fr}}