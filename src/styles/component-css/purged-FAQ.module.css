.faqWrapper{padding:var(--space-16)0;position:relative;overflow:hidden}.backgroundGradient{position:absolute;top:-30%;left:-10%;width:70%;height:70%;background:radial-gradient(circle at center,rgba(255,153,0,.08),transparent 70%);filter:blur(60px);z-index:0}.textOrg{text-align:center;margin-bottom:var(--space-10);position:relative;z-index:1}.gradientText{font-size:var(--text-4xl);font-weight:var(--font-bold);margin-bottom:var(--space-4);background:linear-gradient(to right,var(--accent-orange),var(--accent-yellow));background-clip:text;color:transparent}.subtitle{font-size:var(--text-xl);color:var(--text-tertiary);margin-top:var(--space-4);max-width:600px;margin-left:auto;margin-right:auto}.faqList{max-width:800px;margin:0 auto;padding:0 var(--space-4);position:relative;z-index:1}.faqItem{background:rgba(255,255,255,.03);border-radius:var(--radius-lg);margin-bottom:var(--space-4);overflow:hidden;cursor:pointer;transition:transform var(--transition-normal);border:1px solid var(--border-light)}.faqItem:hover{background:rgba(255,255,255,.05);transform:translateY(-2px);border-color:var(--border-medium);box-shadow:var(--shadow-sm)}.faqQuestion{display:flex;justify-content:space-between;align-items:center;padding:var(--space-6)}.questionText{margin:0;font-size:var(--text-lg);color:var(--text-primary);font-weight:var(--font-medium)}.faqIcon{font-size:var(--text-xl);color:var(--accent-orange);transition:transform var(--transition-normal);width:24px;height:24px;display:flex;align-items:center;justify-content:center;background:rgba(255,153,0,.1);border-radius:50%}.open .faqIcon{transform:rotate(180deg);background:rgba(255,153,0,.2)}.faqAnswer{max-height:0;opacity:0;transition:all var(--transition-normal);padding:0 var(--space-6);overflow:hidden}.open .faqAnswer{max-height:500px;opacity:1;padding:0 var(--space-6) var(--space-6)}.answerText{margin:0;color:var(--text-secondary);line-height:var(--leading-relaxed);font-size:var(--text-base)}@media (max-width:768px){.faqWrapper{padding:var(--space-12)0}.gradientText{font-size:var(--text-3xl)}.subtitle{font-size:var(--text-lg)}.questionText{font-size:var(--text-base)}.faqQuestion{padding:var(--space-4)}.open .faqAnswer{padding:0 var(--space-4) var(--space-4)}}@media (max-width:480px){.faqWrapper{padding:var(--space-8)0}.gradientText{font-size:var(--text-2xl)}.subtitle{font-size:var(--text-base)}.faqList{padding:0 var(--space-2)}}