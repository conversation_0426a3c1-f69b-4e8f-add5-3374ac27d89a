.pricingPageWrapper{padding:var(--space-16)0;position:relative;overflow:hidden;background-color:var(--bg-surface);margin-top:var(--space-12)}.backgroundGradient{position:absolute;top:-30%;right:-10%;width:70%;height:70%;background:radial-gradient(circle at center,rgba(25,253,178,.05),transparent 70%);filter:blur(60px);z-index:0}.container{max-width:var(--container-lg);margin:0 auto;padding:0 var(--space-4);position:relative;z-index:1;margin-top:var(--space-12)}.pageHeading{font-size:var(--text-4xl);font-weight:var(--font-bold);margin-bottom:var(--space-8);background:linear-gradient(to right,var(--primary),var(--secondary));background-clip:text;color:transparent;text-align:center}.pricingIntro{max-width:800px;margin:0 auto var(--space-12);text-align:center}.pricingIntroText{font-size:var(--text-lg);color:var(--text-secondary);line-height:var(--leading-relaxed)}.pricingDetailsSection{margin-top:var(--space-16);padding-top:var(--space-16);border-top:1px solid rgba(255,255,255,.05)}.sectionHeading{font-size:var(--text-2xl);font-weight:var(--font-semibold);margin-bottom:var(--space-8);color:var(--text-primary);text-align:center}.detailsGrid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:var(--space-6);margin-top:var(--space-8)}.detailCard{background:rgba(255,255,255,.03);border:1px solid rgba(255,255,255,.05);border-radius:var(--radius-lg);padding:var(--space-6);transition:box-shadow var(--transition-normal)}.detailCard:hover{transform:translateY(-4px);box-shadow:var(--shadow-md);border-color:rgba(255,255,255,.1);background:rgba(255,255,255,.05)}.detailIcon{width:48px;height:48px;border-radius:var(--radius-md);background:rgba(25,253,178,.1);display:flex;align-items:center;justify-content:center;margin-bottom:var(--space-4)}.detailIcon i{font-size:var(--text-xl);color:var(--primary)}.detailTitle{font-size:var(--text-lg);font-weight:var(--font-medium);margin-bottom:var(--space-3);color:var(--text-primary)}.detailText{font-size:var(--text-base);color:var(--text-tertiary);line-height:var(--leading-relaxed)}.faqSection{margin-top:var(--space-16);padding-top:var(--space-16);border-top:1px solid rgba(255,255,255,.05)}.faqList{max-width:800px;margin:var(--space-8) auto 0}.faqItem{border-bottom:1px solid rgba(255,255,255,.05);padding:var(--space-4)0}.faqQuestion{display:flex;justify-content:space-between;align-items:center;cursor:pointer;padding:var(--space-2)0}.questionText{font-size:var(--text-lg);font-weight:var(--font-medium);color:var(--text-primary)}.faqIcon{color:var(--primary);transition:transform var(--transition-normal)}.faqItem.open .faqIcon{transform:rotate(180deg)}.faqAnswer{max-height:0;overflow:hidden;transition:max-height var(--transition-normal),padding var(--transition-normal);color:var(--text-tertiary);font-size:var(--text-base);line-height:var(--leading-relaxed)}.faqItem.open .faqAnswer{max-height:500px;padding-bottom:var(--space-4)}@media (max-width:768px){.pricingPageWrapper{padding:var(--space-12)0}.pageHeading{font-size:var(--text-3xl)}.pricingIntroText{font-size:var(--text-base)}.detailsGrid{gap:var(--space-4)}.detailCard{padding:var(--space-4)}}@media (max-width:480px){.pricingPageWrapper{padding:var(--space-8)0}.pageHeading{font-size:var(--text-2xl)}.sectionHeading{font-size:var(--text-xl)}.detailTitle{font-size:var(--text-base)}.detailText{font-size:var(--text-sm)}.questionText{font-size:var(--text-base)}}