.servicesSection{padding:var(--section-spacing-y)0;background-color:var(--bg);position:relative}.container{width:100%}.sectionHeading{text-align:center;margin-bottom:4rem}.sectionTitle{font-family:var(--font-bwgradual);font-size:var(--text-section-heading);font-weight:600;color:var(--fg);margin:1rem 0;letter-spacing:-.01em}.sectionDescription{font-size:1.125rem;color:var(--text-secondary);max-width:600px;margin:0 auto;line-height:var(--leading-relaxed)}.servicesGrid{display:grid;grid-template-columns:repeat(2,1fr);gap:2rem;margin-bottom:4rem}.serviceCard{background:var(--surface-dim);border:1px solid var(--border-soft);border-radius:1.5rem;padding:2rem;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px);transition:all .3s ease;display:flex;flex-direction:column;height:100%}.serviceCard:hover{background:rgba(255,255,255,.05);border-color:var(--border-strong);transform:translateY(-4px);box-shadow:0 20px 60px -40px rgba(47,129,247,.9)}.cardHeader{display:flex;align-items:center;gap:1rem;margin-bottom:1.5rem}.iconWrapper{width:48px;height:48px;border-radius:12px;background:var(--surface-dim);border:1px solid var(--border-soft);display:flex;align-items:center;justify-content:center;flex-shrink:0}.iconWrapper i{font-size:1.5rem;color:var(--violet)}.serviceTitle{font-family:var(--font-bwgradual);font-size:1.25rem;font-weight:600;color:var(--fg);margin:0}.serviceDescription{color:var(--text-secondary);line-height:var(--leading-relaxed);margin-bottom:1.5rem;font-size:1rem}.serviceBullets{list-style:none;padding:0;margin:0 0 2rem 0;flex:1}.serviceBullets li{color:var(--text-tertiary);font-size:.875rem;margin-bottom:.5rem;position:relative;padding-left:1.5rem}.serviceBullets li::before{content:"•";color:var(--violet);position:absolute;left:0;top:0}.cardFooter{margin-top:auto}.microCTA{display:inline-flex;align-items:center;gap:.5rem;color:var(--violet);text-decoration:none;font-size:.875rem;font-weight:500;transition:all .3s ease}.microCTA:hover{color:var(--fg);transform:translateX(4px)}.microCTA i{font-size:.75rem;transition:transform .3s ease}.microCTA:hover i{transform:translateX(2px)}.bottomCTA{display:flex;align-items:center;justify-content:space-between;padding:2rem;background:var(--surface-dim);border:1px solid var(--border-soft);border-radius:1.5rem;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px)}.ctaContent h3{font-family:var(--font-bwgradual);font-size:1.25rem;font-weight:600;color:var(--fg);margin:0 0 .5rem 0}.ctaContent p{color:var(--text-secondary);margin:0;font-size:.875rem}@media (max-width:768px){.servicesGrid{grid-template-columns:1fr;gap:1.5rem}.serviceCard{padding:1.5rem}.cardHeader{gap:.75rem;margin-bottom:1rem}.iconWrapper{width:40px;height:40px}.iconWrapper i{font-size:1.25rem}.serviceTitle{font-size:1.125rem}.serviceDescription{font-size:.875rem;margin-bottom:1rem}.serviceBullets{margin-bottom:1.5rem}.serviceBullets li{font-size:.8rem}.bottomCTA{flex-direction:column;gap:1.5rem;text-align:center}.bottomCTA .btn{width:100%;max-width:280px}}@media (max-width:480px){.sectionHeading{margin-bottom:3rem}.sectionTitle{font-size:clamp(1.75rem,4vw,2.25rem)}.sectionDescription{font-size:1rem}.serviceCard{padding:1.25rem}.cardHeader{flex-direction:column;align-items:flex-start;gap:1rem}.iconWrapper{width:36px;height:36px}.iconWrapper i{font-size:1.125rem}.serviceTitle{font-size:1rem}.serviceDescription{font-size:.8rem}.serviceBullets li{font-size:.75rem}.bottomCTA{padding:1.5rem}.ctaContent h3{font-size:1.125rem}.ctaContent p{font-size:.8rem}}@media (prefers-reduced-motion:reduce){.serviceCard,.microCTA{transition:none}.serviceCard:hover{transform:none}.microCTA:hover{transform:none}}