name: Deploy to GitHub Pages

on:
  push:
    branches:
      - main  # Set this to your default branch

permissions:
  contents: write

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v3

      - name: Setup Node.js ⚙️
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'

      - name: Install dependencies 📦
        run: npm ci

      - name: Build 🔧
        run: npm run deploy
        env:
          NODE_ENV: production
          # Ensure consistent build ID for stable file names
          NEXT_PUBLIC_BUILD_ID: stable-build

      - name: Verify asset paths 🔍
        run: |
          # Verify asset paths have been fixed
          echo "Verifying asset paths in HTML files..."
          grep -r "https://flat18.co.uk/_next" out/ --include="*.html" || echo "No asset paths found, running fix script again"

          # Run the fix script again to ensure all paths are fixed
          node scripts/fix-asset-paths.js

          # Check if CNAME file exists
          if [ ! -f out/CNAME ]; then
            echo "flat18.co.uk" > out/CNAME
          fi

          # Check if .nojekyll file exists
          if [ ! -f out/.nojekyll ]; then
            touch out/.nojekyll
          fi

          # Check if _headers file exists
          if [ ! -f out/_headers ]; then
            cp public/_headers out/
          fi

      - name: Deploy 🚀
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: out  # The folder the action should deploy
          branch: gh-pages  # The branch the action should deploy to
          clean: true  # Automatically remove deleted files from the deploy branch
